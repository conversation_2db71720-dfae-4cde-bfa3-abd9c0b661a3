// 配比相关类型定义

// 搅拌站信息
export interface MixingStation {
  id: string;
  name: string;
  code: string;
  isActive: boolean;
}

// 材料类型
export type MaterialType = 'cement' | 'water' | 'sand' | 'stone' | 'additive' | 'admixture' | 'flyash' | 'slag' | 'silica' | 'expansion' | 'accelerator' | 'ultrafine';

// 材料信息
export interface Material {
  id: string;
  name: string;
  code: string;
  type: MaterialType;
  specification: string;
  density: number; // 密度 kg/m³
  sequence: number; // 显示序号
  stationId: string; // 所属搅拌站
}

// 料仓信息
export interface SiloInfo {
  id: string;
  identifier: string; // 罐标识，如：粉料1、骨料1、水1、外加剂1
  name: string; // 罐名称，如：站1#水泥、站2#机制砂
  displayOrder: number; // 显示次序
  isEnabled: boolean; // 是否启用
  stationId: string; // 所属搅拌站
  materialType: MaterialType;
}

// 配比计算参数
export interface MixCalculationParams {
  density: number; // 密度 kg/m³
  totalWeight: number; // 总重量 kg
  mixingTime: number; // 搅拌时间 秒
  waterCementRatio: number; // 水胶比
  waterContent: number; // 用水量 kg/m³
  sandRatio: number; // 沙率 %
  additiveRatio: number; // 外加剂% 
  antifreezeRatio: number; // 防冻剂%
  flyashRatio: number; // 粉煤灰%
  slagRatio: number; // 矿粉%
  silicaRatio: number; // 矿S105%
  expansionRatio: number; // 膨胀剂%
  acceleratorRatio: number; // 早强剂%
  ultrafineRatio: number; // 超细砂%
}

// 计算方法
export type CalculationMethod = 
  | 'no_flyash_excess' // 不考虑粉煤灰超量系数
  | 'consider_flyash' // 考虑粉煤灰系数
  | 'flyash_volume_conversion' // 考虑粉煤灰系数且体积折算
  | 'excess_flyash'; // 超量煤灰

// 配比材料项
export interface MixRatioItem {
  id: string;
  materialId: string;
  materialName: string;
  specification: string;
  theoreticalAmount: number; // 理论量 kg/m³
  waterContent: number; // 含水率 %
  stoneContent: number; // 含石率 %
  actualAmount: number; // 实际量 kg/m³
  designValue: number; // 设计值 kg/m³
  storageLocation: string; // 存储位置
  siloId?: string; // 料仓ID
  stationName?: string; // 站名
  stationCode?: string; // 站号
}

// 配比信息
export interface MixRatio {
  id: string;
  code: string; // 配比编号
  taskId: string; // 任务ID
  stationId: string; // 搅拌站ID
  
  // 任务信息
  taskNumber: string;
  projectName: string;
  strength: string; // 强度等级
  impermeability: string; // 抗渗
  freezeResistance: string; // 抗冻
  constructionSite: string; // 施工部位
  slump: number; // 坍落度
  pouringMethod: string; // 浇筑方式
  
  // 计算参数
  calculationParams: MixCalculationParams;
  calculationMethod: CalculationMethod;
  
  // 配比材料
  items: MixRatioItem[];
  
  // 分配比例
  distributionRatio: Record<string, number>;
  
  // 其他信息
  productionTips: string; // 搅拌生产时提示内容
  serialNumber: string; // 流水号
  createdBy: string; // 创建人
  createdAt: string; // 创建时间
  lastModifiedBy: string; // 最后修改人
  lastModifiedAt: string; // 最后修改时间
  isSync: boolean; // 是否同步
  status: 'draft' | 'approved' | 'sent'; // 状态：草稿、审核、发送
}

// 配比历史记录
export interface MixRatioHistory {
  id: string;
  mixRatioId: string;
  taskId: string;
  operator: string; // 录入人
  modifiedAt: string; // 修改时间
  price: number; // 价格
  materials: Record<string, number>; // 各种材料用量
  changes: string; // 修改内容描述
}

// 物料信息（右侧显示）
export interface MaterialInfo {
  material: string;
  specifications: string[];
  siloName: string;
  stationId: string;
  stationName: string;
}

// 检测标准
export interface QualityStandard {
  id: string;
  name: string;
  standards: Record<string, any>; // 各项检测标准
  isActive: boolean;
}
