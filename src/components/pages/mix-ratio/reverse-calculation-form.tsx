'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Calculator, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

import type { CalculationMethod } from '@/types/mixRatio';

interface ReverseCalculationFormProps {
  isOpen: boolean;
  onClose: () => void;
  onCalculate: (params: CalculationParams) => void;
  currentParams: {
    density: number;
    waterCementRatio: number;
    waterContent: number;
    sandRatio: number;
    additiveRatio: number;
  };
}

interface CalculationParams {
  density: number;
  waterCementRatio: number;
  waterContent: number;
  sandRatio: number;
  additiveRatio: number;
  antifreezeRatio: number;
  flyashRatio: number;
  calculationMethod: CalculationMethod;
}

export function ReverseCalculationForm({
  isOpen,
  onClose,
  onCalculate,
  currentParams
}: ReverseCalculationFormProps) {
  const [params, setParams] = useState<CalculationParams>({
    density: currentParams.density || 2.36,
    waterCementRatio: currentParams.waterCementRatio || 0.45,
    waterContent: currentParams.waterContent || 180,
    sandRatio: currentParams.sandRatio || 35,
    additiveRatio: currentParams.additiveRatio || 1.3,
    antifreezeRatio: 0,
    flyashRatio: 0,
    calculationMethod: 'no_flyash_excess'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const calculationMethods = [
    { value: 'no_flyash_excess', label: '不考虑粉煤灰超量系数' },
    { value: 'consider_flyash', label: '考虑粉煤灰系数' },
    { value: 'flyash_volume_conversion', label: '考虑粉煤灰系数且体积折算' },
    { value: 'excess_flyash', label: '超量煤灰' }
  ];

  const validateParams = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (params.density <= 0 || params.density > 5) {
      newErrors.density = '密度应在0-5之间';
    }
    if (params.waterCementRatio <= 0 || params.waterCementRatio > 1) {
      newErrors.waterCementRatio = '水胶比应在0-1之间';
    }
    if (params.waterContent <= 0 || params.waterContent > 300) {
      newErrors.waterContent = '用水量应在0-300之间';
    }
    if (params.sandRatio <= 0 || params.sandRatio > 100) {
      newErrors.sandRatio = '沙率应在0-100之间';
    }
    if (params.additiveRatio < 0 || params.additiveRatio > 10) {
      newErrors.additiveRatio = '外加剂比例应在0-10之间';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCalculate = () => {
    if (validateParams()) {
      onCalculate(params);
      onClose();
    }
  };

  const updateParam = (key: keyof CalculationParams, value: any) => {
    setParams(prev => ({ ...prev, [key]: value }));
    // 清除对应字段的错误
    if (errors[key]) {
      setErrors(prev => ({ ...prev, [key]: '' }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            反算参数设置
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              请填写完整的计算参数，系统将根据这些参数自动计算各材料用量
            </AlertDescription>
          </Alert>

          <Card>
            <CardContent className="p-4 space-y-3">
              {/* 基础参数 */}
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1">
                  <Label className="text-xs">密度 (t/m³)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={params.density}
                    onChange={(e) => updateParam('density', Number(e.target.value))}
                    className={`h-7 text-xs ${errors.density ? 'border-red-500' : ''}`}
                  />
                  {errors.density && (
                    <div className="text-xs text-red-500">{errors.density}</div>
                  )}
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">水胶比</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={params.waterCementRatio}
                    onChange={(e) => updateParam('waterCementRatio', Number(e.target.value))}
                    className={`h-7 text-xs ${errors.waterCementRatio ? 'border-red-500' : ''}`}
                  />
                  {errors.waterCementRatio && (
                    <div className="text-xs text-red-500">{errors.waterCementRatio}</div>
                  )}
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">用水量 (kg)</Label>
                  <Input
                    type="number"
                    value={params.waterContent}
                    onChange={(e) => updateParam('waterContent', Number(e.target.value))}
                    className={`h-7 text-xs ${errors.waterContent ? 'border-red-500' : ''}`}
                  />
                  {errors.waterContent && (
                    <div className="text-xs text-red-500">{errors.waterContent}</div>
                  )}
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">沙率 (%)</Label>
                  <Input
                    type="number"
                    value={params.sandRatio}
                    onChange={(e) => updateParam('sandRatio', Number(e.target.value))}
                    className={`h-7 text-xs ${errors.sandRatio ? 'border-red-500' : ''}`}
                  />
                  {errors.sandRatio && (
                    <div className="text-xs text-red-500">{errors.sandRatio}</div>
                  )}
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">外加剂 (%)</Label>
                  <Input
                    type="number"
                    step="0.1"
                    value={params.additiveRatio}
                    onChange={(e) => updateParam('additiveRatio', Number(e.target.value))}
                    className={`h-7 text-xs ${errors.additiveRatio ? 'border-red-500' : ''}`}
                  />
                  {errors.additiveRatio && (
                    <div className="text-xs text-red-500">{errors.additiveRatio}</div>
                  )}
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">防冻剂 (%)</Label>
                  <Input
                    type="number"
                    step="0.1"
                    value={params.antifreezeRatio}
                    onChange={(e) => updateParam('antifreezeRatio', Number(e.target.value))}
                    className="h-7 text-xs"
                  />
                </div>
              </div>

              {/* 计算方法 */}
              <div className="space-y-1">
                <Label className="text-xs">计算方法</Label>
                <Select
                  value={params.calculationMethod}
                  onValueChange={(value: CalculationMethod) => updateParam('calculationMethod', value)}
                >
                  <SelectTrigger className="h-7 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {calculationMethods.map((method) => (
                      <SelectItem key={method.value} value={method.value}>
                        {method.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* 预览计算结果 */}
          <Card className="bg-muted/50">
            <CardContent className="p-3">
              <div className="text-xs text-muted-foreground space-y-1">
                <div className="font-medium">预计计算结果：</div>
                <div>水泥用量: {Math.round(params.waterContent / params.waterCementRatio)} kg</div>
                <div>总重量: {Math.round(params.density * 1000)} kg</div>
                <div>外加剂用量: {Math.round(params.waterContent / params.waterCementRatio * params.additiveRatio / 100)} kg</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose} size="sm">
            取消
          </Button>
          <Button onClick={handleCalculate} size="sm">
            <Calculator className="mr-2 h-3 w-3" />
            开始反算
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
