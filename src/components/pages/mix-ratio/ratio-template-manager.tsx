'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Star, 
  Download,
  FileText,
  CheckCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface RatioTemplate {
  id: string;
  name: string;
  code: string;
  strength: string;
  category: 'standard' | 'economic' | 'high_performance' | 'special';
  description: string;
  isDefault: boolean;
  isFavorite: boolean;
  createdBy: string;
  createdAt: string;
  usageCount: number;
  parameters: {
    density: number;
    waterRatio: number;
    waterAmount: number;
    sandRatio: number;
    additiveRatio: number;
    flyashRatio: number;
    antifreezeRatio: number;
    totalWeight: number;
    mixingTime: number;
  };
  materials: Array<{
    name: string;
    type: string;
    amount: number;
    waterContent: number;
  }>;
}

interface RatioTemplateManagerProps {
  onClose: () => void;
  onApplyTemplate?: (template: RatioTemplate) => void;
}

export function RatioTemplateManager({ onClose, onApplyTemplate }: RatioTemplateManagerProps) {
  const [templates, setTemplates] = useState<RatioTemplate[]>([
    {
      id: '1',
      name: 'C30标准配比',
      code: 'STD-C30-001',
      strength: 'C30',
      category: 'standard',
      description: '通用C30混凝土标准配比，适用于一般结构工程',
      isDefault: true,
      isFavorite: true,
      createdBy: '系统管理员',
      createdAt: '2024-01-01',
      usageCount: 156,
      parameters: {
        density: 2.36,
        waterRatio: 0.45,
        waterAmount: 180,
        sandRatio: 35,
        additiveRatio: 1.3,
        flyashRatio: 0,
        antifreezeRatio: 0,
        totalWeight: 2360,
        mixingTime: 95
      },
      materials: [
        { name: '水泥', type: 'powder', amount: 400, waterContent: 0 },
        { name: '水', type: 'water', amount: 180, waterContent: 0 },
        { name: '中砂', type: 'aggregate', amount: 650, waterContent: 3.5 },
        { name: '石子', type: 'aggregate', amount: 1100, waterContent: 1.0 },
        { name: '减水剂', type: 'additive', amount: 5.2, waterContent: 0 }
      ]
    },
    {
      id: '2',
      name: 'C25经济配比',
      code: 'ECO-C25-001',
      strength: 'C25',
      category: 'economic',
      description: '经济型C25配比，含粉煤灰，成本较低',
      isDefault: false,
      isFavorite: true,
      createdBy: '技术部',
      createdAt: '2024-01-05',
      usageCount: 89,
      parameters: {
        density: 2.35,
        waterRatio: 0.50,
        waterAmount: 175,
        sandRatio: 38,
        additiveRatio: 1.0,
        flyashRatio: 15,
        antifreezeRatio: 0,
        totalWeight: 2350,
        mixingTime: 90
      },
      materials: [
        { name: '水泥', type: 'powder', amount: 300, waterContent: 0 },
        { name: '粉煤灰', type: 'powder', amount: 50, waterContent: 0 },
        { name: '水', type: 'water', amount: 175, waterContent: 0 },
        { name: '中砂', type: 'aggregate', amount: 680, waterContent: 3.0 },
        { name: '石子', type: 'aggregate', amount: 1110, waterContent: 1.0 },
        { name: '减水剂', type: 'additive', amount: 3.5, waterContent: 0 }
      ]
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'standard': return 'bg-blue-100 text-blue-800';
      case 'economic': return 'bg-green-100 text-green-800';
      case 'high_performance': return 'bg-purple-100 text-purple-800';
      case 'special': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'standard': return '标准';
      case 'economic': return '经济';
      case 'high_performance': return '高性能';
      case 'special': return '特殊';
      default: return '未知';
    }
  };

  const filteredTemplates = templates.filter(template => 
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleApply = (template: RatioTemplate) => {
    onApplyTemplate?.(template);
    setTemplates(prev => prev.map(t => 
      t.id === template.id ? { ...t, usageCount: t.usageCount + 1 } : t
    ));
    toast({
      title: '应用成功',
      description: `已应用配比模板: ${template.name}`
    });
    onClose();
  };

  const handleToggleFavorite = (id: string) => {
    setTemplates(prev => prev.map(t => 
      t.id === id ? { ...t, isFavorite: !t.isFavorite } : t
    ));
  };

  const handleDelete = (id: string) => {
    setTemplates(prev => prev.filter(t => t.id !== id));
    toast({
      title: '删除成功',
      description: '配比模板已删除'
    });
  };

  const handleCopy = (template: RatioTemplate) => {
    const newTemplate: RatioTemplate = {
      ...template,
      id: Date.now().toString(),
      name: `${template.name} (副本)`,
      code: `${template.code}-COPY`,
      isDefault: false,
      isFavorite: false,
      createdBy: '当前用户',
      createdAt: new Date().toISOString().split('T')[0],
      usageCount: 0
    };
    setTemplates(prev => [...prev, newTemplate]);
    toast({
      title: '复制成功',
      description: '配比模板已复制'
    });
  };

  return (
    <div className="space-y-4">
      {/* 搜索和操作 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between space-x-4">
            <div className="flex items-center space-x-4 flex-1">
              <Input
                placeholder="搜索模板名称、编号或描述..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
            
            <div className="flex space-x-2">
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                新建模板
              </Button>
              <Button size="sm" variant="outline">
                <Download className="mr-2 h-4 w-4" />
                导出
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 模板列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-base">{template.name}</CardTitle>
                    {template.isDefault && (
                      <Badge variant="secondary" className="text-xs">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        默认
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getCategoryColor(template.category)}>
                      {getCategoryLabel(template.category)}
                    </Badge>
                    <Badge variant="outline">{template.strength}</Badge>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleToggleFavorite(template.id)}
                  className={template.isFavorite ? 'text-yellow-500' : 'text-gray-400'}
                >
                  <Star className={`h-4 w-4 ${template.isFavorite ? 'fill-current' : ''}`} />
                </Button>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-3">
              <div className="text-sm text-muted-foreground">
                {template.description}
              </div>
              
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span>编号:</span>
                  <code className="bg-muted px-1 py-0.5 rounded">{template.code}</code>
                </div>
                <div className="flex justify-between">
                  <span>使用次数:</span>
                  <span className="font-medium">{template.usageCount}</span>
                </div>
                <div className="flex justify-between">
                  <span>创建者:</span>
                  <span>{template.createdBy}</span>
                </div>
              </div>
              
              <div className="flex justify-between pt-2 border-t">
                <div className="flex space-x-1">
                  <Button size="sm" variant="outline" onClick={() => handleCopy(template)}>
                    <Copy className="h-3 w-3" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Edit className="h-3 w-3" />
                  </Button>
                  {!template.isDefault && (
                    <Button size="sm" variant="destructive" onClick={() => handleDelete(template.id)}>
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
                <Button size="sm" onClick={() => handleApply(template)}>
                  应用
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <div className="text-lg font-medium mb-2">未找到匹配的模板</div>
            <div className="text-muted-foreground mb-4">
              尝试调整搜索条件或创建新的配比模板
            </div>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              创建新模板
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
