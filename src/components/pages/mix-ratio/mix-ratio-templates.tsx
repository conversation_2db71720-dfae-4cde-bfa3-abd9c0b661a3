'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  FileText, 
  Plus, 
  Star, 
  Clock, 
  Copy,
  Trash2,
  Search,
  Filter
} from 'lucide-react';
import type { MixRatio, MixCalculationParams } from '@/types/mixRatio';

export interface MixRatioTemplate {
  id: string;
  name: string;
  description: string;
  strength: string;
  category: 'standard' | 'custom' | 'favorite';
  calculationParams: MixCalculationParams;
  materials: Array<{
    materialName: string;
    specification: string;
    theoreticalAmount: number;
  }>;
  createdAt: string;
  createdBy: string;
  usageCount: number;
  tags: string[];
}

interface MixRatioTemplatesProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyTemplate: (template: MixRatioTemplate) => void;
  onSaveAsTemplate?: (mixRatio: MixRatio) => void;
  currentMixRatio?: MixRatio;
}

export function MixRatioTemplates({
  isOpen,
  onClose,
  onApplyTemplate,
  onSaveAsTemplate,
  currentMixRatio
}: MixRatioTemplatesProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'standard' | 'custom' | 'favorite'>('all');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [newTemplateName, setNewTemplateName] = useState('');
  const [newTemplateDescription, setNewTemplateDescription] = useState('');

  // 模拟模板数据
  const templates: MixRatioTemplate[] = [
    {
      id: 'template-1',
      name: 'C30普通混凝土',
      description: '适用于一般结构工程',
      strength: 'C30',
      category: 'standard',
      calculationParams: {
        density: 2.4,
        totalWeight: 2400,
        mixingTime: 120,
        waterCementRatio: 0.45,
        waterContent: 180,
        sandRatio: 35,
        additiveRatio: 1.2,
        antifreezeRatio: 0,
        flyashRatio: 15,
        slagRatio: 10,
        silicaRatio: 0,
        expansionRatio: 0,
        acceleratorRatio: 0,
        ultrafineRatio: 0
      },
      materials: [
        { materialName: 'P.O 42.5水泥', specification: 'P.O 42.5', theoreticalAmount: 350 },
        { materialName: '水', specification: '自来水', theoreticalAmount: 180 },
        { materialName: '中砂', specification: '2.5-5mm', theoreticalAmount: 650 },
        { materialName: '碎石', specification: '5-25mm', theoreticalAmount: 1200 }
      ],
      createdAt: '2024-01-01',
      createdBy: 'system',
      usageCount: 156,
      tags: ['常用', '结构']
    },
    {
      id: 'template-2',
      name: 'C40高强混凝土',
      description: '适用于高层建筑主体结构',
      strength: 'C40',
      category: 'standard',
      calculationParams: {
        density: 2.45,
        totalWeight: 2450,
        mixingTime: 150,
        waterCementRatio: 0.38,
        waterContent: 165,
        sandRatio: 32,
        additiveRatio: 1.8,
        antifreezeRatio: 0,
        flyashRatio: 20,
        slagRatio: 15,
        silicaRatio: 5,
        expansionRatio: 0,
        acceleratorRatio: 0,
        ultrafineRatio: 0
      },
      materials: [
        { materialName: 'P.O 52.5水泥', specification: 'P.O 52.5', theoreticalAmount: 420 },
        { materialName: '水', specification: '自来水', theoreticalAmount: 165 },
        { materialName: '中砂', specification: '2.5-5mm', theoreticalAmount: 620 },
        { materialName: '碎石', specification: '5-25mm', theoreticalAmount: 1180 }
      ],
      createdAt: '2024-01-02',
      createdBy: 'system',
      usageCount: 89,
      tags: ['高强', '主体']
    }
  ];

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = searchTerm === '' || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.strength.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const handleSaveAsTemplate = () => {
    if (!currentMixRatio || !newTemplateName.trim()) return;

    const newTemplate: MixRatioTemplate = {
      id: `template-${Date.now()}`,
      name: newTemplateName,
      description: newTemplateDescription,
      strength: currentMixRatio.strength,
      category: 'custom',
      calculationParams: currentMixRatio.calculationParams,
      materials: currentMixRatio.items.map(item => ({
        materialName: item.materialName,
        specification: item.specification,
        theoreticalAmount: item.theoreticalAmount
      })),
      createdAt: new Date().toISOString().split('T')[0],
      createdBy: 'user',
      usageCount: 0,
      tags: []
    };

    onSaveAsTemplate?.(currentMixRatio);
    setShowSaveDialog(false);
    setNewTemplateName('');
    setNewTemplateDescription('');
  };

  const getCategoryIcon = (category: MixRatioTemplate['category']) => {
    switch (category) {
      case 'standard': return <FileText className="h-4 w-4" />;
      case 'favorite': return <Star className="h-4 w-4" />;
      case 'custom': return <Plus className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getCategoryLabel = (category: MixRatioTemplate['category']) => {
    switch (category) {
      case 'standard': return '标准';
      case 'favorite': return '收藏';
      case 'custom': return '自定义';
      default: return '未知';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl max-h-[80vh] overflow-hidden">
        <CardHeader className="border-b">
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              配比模板
            </span>
            <div className="flex items-center space-x-2">
              {currentMixRatio && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowSaveDialog(true)}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  保存为模板
                </Button>
              )}
              <Button variant="ghost" size="sm" onClick={onClose}>
                ×
              </Button>
            </div>
          </CardTitle>
        </CardHeader>

        <div className="flex h-[60vh]">
          {/* 左侧筛选 */}
          <div className="w-64 border-r bg-muted/20 p-4 space-y-4">
            {/* 搜索 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">搜索模板</Label>
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索名称、强度、标签..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* 分类筛选 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">分类</Label>
              <div className="space-y-1">
                {[
                  { value: 'all', label: '全部' },
                  { value: 'standard', label: '标准模板' },
                  { value: 'custom', label: '自定义' },
                  { value: 'favorite', label: '收藏' }
                ].map((category) => (
                  <Button
                    key={category.value}
                    variant={selectedCategory === category.value ? 'default' : 'ghost'}
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => setSelectedCategory(category.value as any)}
                  >
                    {category.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* 右侧模板列表 */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredTemplates.map((template) => (
                <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          {getCategoryIcon(template.category)}
                          <h3 className="font-medium">{template.name}</h3>
                        </div>
                        <p className="text-sm text-muted-foreground">{template.description}</p>
                      </div>
                      <Badge variant="outline">{template.strength}</Badge>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-3">
                    {/* 关键参数 */}
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>水胶比: {template.calculationParams.waterCementRatio}</div>
                      <div>砂率: {template.calculationParams.sandRatio}%</div>
                      <div>密度: {template.calculationParams.density} t/m³</div>
                      <div>用水量: {template.calculationParams.waterContent} kg</div>
                    </div>

                    {/* 标签 */}
                    {template.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {template.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* 使用信息 */}
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {template.createdAt}
                      </span>
                      <span>使用 {template.usageCount} 次</span>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        onClick={() => onApplyTemplate(template)}
                        className="flex-1"
                      >
                        <Copy className="mr-1 h-3 w-3" />
                        应用
                      </Button>
                      {template.category === 'custom' && (
                        <Button variant="outline" size="sm">
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredTemplates.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>未找到匹配的模板</p>
              </div>
            )}
          </div>
        </div>

        {/* 保存模板对话框 */}
        {showSaveDialog && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <Card className="w-96">
              <CardHeader>
                <CardTitle>保存为模板</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>模板名称</Label>
                  <Input
                    value={newTemplateName}
                    onChange={(e) => setNewTemplateName(e.target.value)}
                    placeholder="输入模板名称"
                  />
                </div>
                <div className="space-y-2">
                  <Label>描述</Label>
                  <Input
                    value={newTemplateDescription}
                    onChange={(e) => setNewTemplateDescription(e.target.value)}
                    placeholder="输入模板描述（可选）"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    onClick={handleSaveAsTemplate}
                    disabled={!newTemplateName.trim()}
                    className="flex-1"
                  >
                    保存
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowSaveDialog(false)}
                  >
                    取消
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </Card>
    </div>
  );
}
