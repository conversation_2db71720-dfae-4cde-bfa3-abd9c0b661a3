'use client';

import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle2, 
  AlertTriangle, 
  Info, 
  TrendingUp, 
  TrendingDown,
  Minus,
  ArrowRight
} from 'lucide-react';
import type { ReverseCalculationResult, MixCalculationParams } from './reverse-calculation-engine';

interface ReverseCalculationResultProps {
  result: ReverseCalculationResult;
  originalParams?: MixCalculationParams;
  onApplyResult: (params: MixCalculationParams) => void;
  onClose: () => void;
}

export function ReverseCalculationResultComponent({
  result,
  originalParams,
  onApplyResult,
  onClose
}: ReverseCalculationResultProps) {
  const { calculatedParams, warnings, suggestions, isValid, confidence } = result;

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.8) return '高';
    if (confidence >= 0.6) return '中';
    return '低';
  };

  const formatValue = (value: number, decimals: number = 2) => {
    return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);
  };

  const renderParameterComparison = () => {
    if (!originalParams) return null;

    const comparisons = [
      { key: 'density', name: '密度', unit: 't/m³', decimals: 2 },
      { key: 'waterCementRatio', name: '水胶比', unit: '', decimals: 3 },
      { key: 'waterContent', name: '用水量', unit: 'kg/m³', decimals: 0 },
      { key: 'sandRatio', name: '砂率', unit: '%', decimals: 1 },
      { key: 'additiveRatio', name: '外加剂', unit: '%', decimals: 1 },
      { key: 'flyashRatio', name: '粉煤灰', unit: '%', decimals: 1 },
      { key: 'slagRatio', name: '矿粉', unit: '%', decimals: 1 }
    ];

    return (
      <div className="space-y-3">
        <h4 className="text-sm font-medium">参数对比</h4>
        <div className="space-y-2">
          {comparisons.map(comp => {
            const originalValue = originalParams[comp.key as keyof MixCalculationParams] as number;
            const calculatedValue = calculatedParams[comp.key as keyof MixCalculationParams] as number;
            const difference = calculatedValue - originalValue;
            const percentDiff = originalValue !== 0 ? (difference / originalValue) * 100 : 0;
            const isSignificant = Math.abs(percentDiff) > 5;

            return (
              <div key={comp.key} className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground w-16">{comp.name}</span>
                <div className="flex items-center space-x-2 flex-1">
                  <span className="w-16 text-right">
                    {formatValue(originalValue, comp.decimals)}{comp.unit}
                  </span>
                  <ArrowRight className="h-3 w-3 text-muted-foreground" />
                  <span className="w-16 text-right font-medium">
                    {formatValue(calculatedValue, comp.decimals)}{comp.unit}
                  </span>
                  <div className="flex items-center space-x-1 w-20">
                    {difference > 0 ? (
                      <TrendingUp className={`h-3 w-3 ${isSignificant ? 'text-red-500' : 'text-green-500'}`} />
                    ) : difference < 0 ? (
                      <TrendingDown className={`h-3 w-3 ${isSignificant ? 'text-red-500' : 'text-green-500'}`} />
                    ) : (
                      <Minus className="h-3 w-3 text-muted-foreground" />
                    )}
                    <span className={`text-xs ${isSignificant ? 'text-red-600 font-medium' : 'text-muted-foreground'}`}>
                      {formatValue(Math.abs(percentDiff), 1)}%
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            {isValid ? (
              <CheckCircle2 className="h-5 w-5 text-green-600" />
            ) : (
              <AlertTriangle className="h-5 w-5 text-red-600" />
            )}
            配比反算结果
          </span>
          <Badge variant={isValid ? 'default' : 'destructive'}>
            {isValid ? '有效' : '无效'}
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 可信度指示器 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>计算可信度</span>
            <span className={`font-medium ${getConfidenceColor(confidence)}`}>
              {getConfidenceLabel(confidence)} ({Math.round(confidence * 100)}%)
            </span>
          </div>
          <Progress value={confidence * 100} className="h-2" />
        </div>

        <Separator />

        {/* 计算结果 */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">反算参数</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">密度:</span>
                <span className="font-medium">{formatValue(calculatedParams.density, 2)} t/m³</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">水胶比:</span>
                <span className="font-medium">{formatValue(calculatedParams.waterCementRatio, 3)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">用水量:</span>
                <span className="font-medium">{calculatedParams.waterContent} kg/m³</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">砂率:</span>
                <span className="font-medium">{formatValue(calculatedParams.sandRatio, 1)}%</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">外加剂:</span>
                <span className="font-medium">{formatValue(calculatedParams.additiveRatio, 1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">粉煤灰:</span>
                <span className="font-medium">{formatValue(calculatedParams.flyashRatio, 1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">矿粉:</span>
                <span className="font-medium">{formatValue(calculatedParams.slagRatio, 1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">总重量:</span>
                <span className="font-medium">{calculatedParams.totalWeight} kg/m³</span>
              </div>
            </div>
          </div>
        </div>

        {/* 参数对比 */}
        {originalParams && (
          <>
            <Separator />
            {renderParameterComparison()}
          </>
        )}

        {/* 警告信息 */}
        {warnings.length > 0 && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                警告信息
              </h4>
              <div className="space-y-1">
                {warnings.map((warning, index) => (
                  <div key={index} className="flex items-start gap-2 text-xs text-yellow-700 bg-yellow-50 p-2 rounded">
                    <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <span>{warning}</span>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* 建议信息 */}
        {suggestions.length > 0 && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Info className="h-4 w-4 text-blue-600" />
                优化建议
              </h4>
              <div className="space-y-1">
                {suggestions.map((suggestion, index) => (
                  <div key={index} className="flex items-start gap-2 text-xs text-blue-700 bg-blue-50 p-2 rounded">
                    <Info className="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <span>{suggestion}</span>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        <Separator />

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
          <Button 
            onClick={() => onApplyResult(calculatedParams)}
            disabled={!isValid}
          >
            应用结果
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
