'use client';

import React, { useState, useMemo } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  History,
  Search,
  Download,
  Eye,
  Copy,
  Trash2,
  Calendar as CalendarIcon,
  Filter,
  RefreshCw,
  FileText,
  BarChart3,
  TrendingUp,
  Clock,
  User,
  CheckCircle2,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import type { MixRatio } from '@/types/mixRatio';

interface MixRatioHistoryRecord extends MixRatio {
  version: number;
  changeDescription: string;
  approvedBy?: string;
  approvedAt?: string;
  usageCount: number;
  lastUsed?: string;
}

interface EnhancedMixRatioHistoryProps {
  isOpen: boolean;
  onClose: () => void;
  taskId: string;
  onApplyHistory?: (mixRatio: MixRatio) => void;
  onViewDetails?: (mixRatio: MixRatio) => void;
}

export function EnhancedMixRatioHistory({
  isOpen,
  onClose,
  taskId,
  onApplyHistory,
  onViewDetails
}: EnhancedMixRatioHistoryProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'draft' | 'approved' | 'sent'>('all');
  const [filterStrength, setFilterStrength] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({});
  const [activeTab, setActiveTab] = useState('history');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<MixRatioHistoryRecord | null>(null);
  const { toast } = useToast();

  // 模拟历史记录数据
  const historyRecords: MixRatioHistoryRecord[] = useMemo(() => [
    {
      id: 'mix-1',
      code: 'MR-001-V1',
      taskId: taskId,
      stationId: 'station-1',
      taskNumber: 'T001',
      projectName: '测试工程项目A',
      strength: 'C30',
      impermeability: 'P6',
      freezeResistance: 'F100',
      constructionSite: '地下室基础',
      slump: 180,
      pouringMethod: '泵送',
      calculationParams: {
        density: 2.4,
        totalWeight: 2400,
        mixingTime: 120,
        waterCementRatio: 0.45,
        waterContent: 180,
        sandRatio: 35,
        additiveRatio: 1.2,
        antifreezeRatio: 0,
        flyashRatio: 15,
        slagRatio: 10,
        silicaRatio: 0,
        expansionRatio: 0,
        acceleratorRatio: 0,
        ultrafineRatio: 0
      },
      calculationMethod: 'consider_flyash',
      items: [],
      distributionRatio: {},
      productionTips: '注意控制坍落度',
      serialNumber: 'SN001',
      createdBy: '张工程师',
      createdAt: '2024-01-15T08:00:00Z',
      lastModifiedBy: '李工程师',
      lastModifiedAt: '2024-01-15T10:30:00Z',
      isSync: false,
      status: 'approved',
      version: 1,
      changeDescription: '初始版本',
      approvedBy: '王主管',
      approvedAt: '2024-01-15T11:00:00Z',
      usageCount: 15,
      lastUsed: '2024-01-20T14:30:00Z'
    },
    {
      id: 'mix-2',
      code: 'MR-001-V2',
      taskId: taskId,
      stationId: 'station-1',
      taskNumber: 'T001',
      projectName: '测试工程项目A',
      strength: 'C30',
      impermeability: 'P6',
      freezeResistance: 'F100',
      constructionSite: '地下室基础',
      slump: 180,
      pouringMethod: '泵送',
      calculationParams: {
        density: 2.4,
        totalWeight: 2400,
        mixingTime: 120,
        waterCementRatio: 0.42,
        waterContent: 175,
        sandRatio: 36,
        additiveRatio: 1.5,
        antifreezeRatio: 0,
        flyashRatio: 18,
        slagRatio: 12,
        silicaRatio: 0,
        expansionRatio: 0,
        acceleratorRatio: 0,
        ultrafineRatio: 0
      },
      calculationMethod: 'consider_flyash',
      items: [],
      distributionRatio: {},
      productionTips: '调整外加剂掺量',
      serialNumber: 'SN002',
      createdBy: '李工程师',
      createdAt: '2024-01-16T09:00:00Z',
      lastModifiedBy: '李工程师',
      lastModifiedAt: '2024-01-16T09:30:00Z',
      isSync: false,
      status: 'sent',
      version: 2,
      changeDescription: '优化外加剂掺量，提高工作性',
      approvedBy: '王主管',
      approvedAt: '2024-01-16T10:00:00Z',
      usageCount: 8,
      lastUsed: '2024-01-18T16:20:00Z'
    },
    {
      id: 'mix-3',
      code: 'MR-001-V3',
      taskId: taskId,
      stationId: 'station-1',
      taskNumber: 'T001',
      projectName: '测试工程项目A',
      strength: 'C30',
      impermeability: 'P6',
      freezeResistance: 'F100',
      constructionSite: '地下室基础',
      slump: 180,
      pouringMethod: '泵送',
      calculationParams: {
        density: 2.4,
        totalWeight: 2400,
        mixingTime: 120,
        waterCementRatio: 0.43,
        waterContent: 178,
        sandRatio: 35,
        additiveRatio: 1.3,
        antifreezeRatio: 0,
        flyashRatio: 15,
        slagRatio: 10,
        silicaRatio: 2,
        expansionRatio: 0,
        acceleratorRatio: 0,
        ultrafineRatio: 0
      },
      calculationMethod: 'consider_flyash',
      items: [],
      distributionRatio: {},
      productionTips: '添加硅灰提高强度',
      serialNumber: 'SN003',
      createdBy: '张工程师',
      createdAt: '2024-01-17T14:00:00Z',
      lastModifiedBy: '张工程师',
      lastModifiedAt: '2024-01-17T14:30:00Z',
      isSync: false,
      status: 'draft',
      version: 3,
      changeDescription: '添加硅灰，优化强度发展',
      usageCount: 0
    }
  ], [taskId]);

  const filteredRecords = historyRecords.filter(record => {
    const matchesSearch = searchTerm === '' || 
      record.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.createdBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.changeDescription.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || record.status === filterStatus;
    const matchesStrength = filterStrength === 'all' || record.strength === filterStrength;
    
    const recordDate = new Date(record.createdAt);
    const matchesDateRange = (!dateRange.from || recordDate >= dateRange.from) &&
                            (!dateRange.to || recordDate <= dateRange.to);
    
    return matchesSearch && matchesStatus && matchesStrength && matchesDateRange;
  });

  const strengthOptions = [...new Set(historyRecords.map(r => r.strength))];

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
    toast({
      title: "刷新成功",
      description: "配比历史已更新"
    });
  };

  const handleExport = () => {
    const data = filteredRecords.map(record => ({
      配比编号: record.code,
      版本: record.version,
      强度等级: record.strength,
      创建人: record.createdBy,
      创建时间: format(new Date(record.createdAt), 'yyyy-MM-dd HH:mm'),
      状态: getStatusLabel(record.status),
      使用次数: record.usageCount,
      变更说明: record.changeDescription
    }));
    
    console.log('导出数据:', data);
    toast({
      title: "导出成功",
      description: `已导出 ${data.length} 条历史记录`
    });
  };

  const handleApply = (record: MixRatioHistoryRecord) => {
    onApplyHistory?.(record);
    toast({
      title: "应用成功",
      description: `已应用配比 ${record.code}`
    });
  };

  const handleView = (record: MixRatioHistoryRecord) => {
    setSelectedRecord(record);
    onViewDetails?.(record);
  };

  const handleDelete = (id: string) => {
    // 模拟删除操作
    toast({
      title: "删除成功",
      description: "历史记录已删除"
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">草稿</Badge>;
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">已审核</Badge>;
      case 'sent':
        return <Badge className="bg-blue-100 text-blue-800">已发送</Badge>;
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft': return <Clock className="h-4 w-4 text-gray-600" />;
      case 'approved': return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case 'sent': return <AlertCircle className="h-4 w-4 text-blue-600" />;
      default: return <XCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft': return '草稿';
      case 'approved': return '已审核';
      case 'sent': return '已发送';
      default: return '未知';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <History className="h-5 w-5" />
              配比历史
            </span>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                刷新
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
              >
                <Download className="h-4 w-4 mr-1" />
                导出
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="history">历史记录</TabsTrigger>
            <TabsTrigger value="comparison">版本对比</TabsTrigger>
            <TabsTrigger value="statistics">使用统计</TabsTrigger>
          </TabsList>

          <TabsContent value="history" className="space-y-4 mt-4">
            {/* 筛选和搜索 */}
            <Card>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">搜索</Label>
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="搜索配比编号、项目..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">状态</Label>
                    <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value as any)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部状态</SelectItem>
                        <SelectItem value="draft">草稿</SelectItem>
                        <SelectItem value="approved">已审核</SelectItem>
                        <SelectItem value="sent">已发送</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">强度等级</Label>
                    <Select value={filterStrength} onValueChange={setFilterStrength}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部强度</SelectItem>
                        {strengthOptions.map((strength) => (
                          <SelectItem key={strength} value={strength}>
                            {strength}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">开始日期</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !dateRange.from && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dateRange.from ? format(dateRange.from, "yyyy-MM-dd") : "选择日期"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={dateRange.from}
                          onSelect={(date) => setDateRange({...dateRange, from: date})}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">结束日期</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !dateRange.to && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : "选择日期"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={dateRange.to}
                          onSelect={(date) => setDateRange({...dateRange, to: date})}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-muted-foreground">
                    共 {filteredRecords.length} 条记录
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchTerm('');
                      setFilterStatus('all');
                      setFilterStrength('all');
                      setDateRange({});
                    }}
                  >
                    <Filter className="mr-2 h-4 w-4" />
                    清除筛选
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 历史记录列表 */}
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>配比编号</TableHead>
                      <TableHead>版本</TableHead>
                      <TableHead>强度等级</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建人</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead>使用次数</TableHead>
                      <TableHead>变更说明</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{record.code}</span>
                          </div>
                        </TableCell>

                        <TableCell>
                          <Badge variant="outline">V{record.version}</Badge>
                        </TableCell>

                        <TableCell>
                          <Badge>{record.strength}</Badge>
                        </TableCell>

                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(record.status)}
                            {getStatusBadge(record.status)}
                          </div>
                        </TableCell>

                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span>{record.createdBy}</span>
                          </div>
                        </TableCell>

                        <TableCell>
                          <div className="text-sm">
                            <div>{format(new Date(record.createdAt), 'yyyy-MM-dd')}</div>
                            <div className="text-muted-foreground text-xs">
                              {format(new Date(record.createdAt), 'HH:mm')}
                            </div>
                          </div>
                        </TableCell>

                        <TableCell>
                          <div className="text-center">
                            <div className="font-medium">{record.usageCount}</div>
                            {record.lastUsed && (
                              <div className="text-xs text-muted-foreground">
                                最后: {format(new Date(record.lastUsed), 'MM-dd')}
                              </div>
                            )}
                          </div>
                        </TableCell>

                        <TableCell>
                          <div className="max-w-48 truncate text-sm" title={record.changeDescription}>
                            {record.changeDescription}
                          </div>
                        </TableCell>

                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline" onClick={() => handleView(record)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" onClick={() => handleApply(record)}>
                              <Copy className="h-4 w-4" />
                            </Button>
                            {record.status === 'draft' && (
                              <Button size="sm" variant="destructive" onClick={() => handleDelete(record.id)}>
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="comparison" className="space-y-4 mt-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">版本对比</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>选择两个版本进行对比</p>
                  <p className="text-xs mt-2">功能开发中...</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="statistics" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">总版本数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{historyRecords.length}</div>
                  <div className="text-xs text-muted-foreground">
                    最新版本: V{Math.max(...historyRecords.map(r => r.version))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">总使用次数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {historyRecords.reduce((sum, r) => sum + r.usageCount, 0)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <TrendingUp className="inline h-3 w-3 mr-1" />
                    较上月 +25%
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">最常用版本</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-lg font-bold">
                    {historyRecords.length > 0 &&
                      `V${historyRecords.sort((a, b) => b.usageCount - a.usageCount)[0]?.version}`
                    }
                  </div>
                  <div className="text-xs text-muted-foreground">
                    使用 {historyRecords.length > 0 ? historyRecords.sort((a, b) => b.usageCount - a.usageCount)[0]?.usageCount : 0} 次
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">平均使用频率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {historyRecords.length > 0 ?
                      Math.round(historyRecords.reduce((sum, r) => sum + r.usageCount, 0) / historyRecords.length) : 0
                    }
                  </div>
                  <div className="text-xs text-muted-foreground">
                    次/版本
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">版本使用趋势</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {historyRecords.map((record) => {
                    const maxUsage = Math.max(...historyRecords.map(r => r.usageCount));
                    const percentage = maxUsage > 0 ? (record.usageCount / maxUsage) * 100 : 0;

                    return (
                      <div key={record.id} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <div className="flex items-center space-x-2">
                            <span>{record.code}</span>
                            {getStatusBadge(record.status)}
                          </div>
                          <span>{record.usageCount} 次</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
