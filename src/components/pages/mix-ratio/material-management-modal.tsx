'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, Trash2, Edit, Save, X, Printer, Users } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

import type { Material, MixingStation, MaterialType } from '@/types/mixRatio';

interface MaterialManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedStation: string;
  stations: MixingStation[];
  materials: Material[];
  onMaterialsChange: (materials: Material[]) => void;
}

export function MaterialManagementModal({
  isOpen,
  onClose,
  selectedStation,
  stations,
  materials,
  onMaterialsChange
}: MaterialManagementModalProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingMaterial, setEditingMaterial] = useState<Partial<Material>>({});
  const [currentStation, setCurrentStation] = useState(selectedStation);
  const { toast } = useToast();

  const materialTypes: { value: MaterialType; label: string }[] = [
    { value: 'cement', label: '水泥' },
    { value: 'water', label: '水' },
    { value: 'sand', label: '砂' },
    { value: 'stone', label: '石子' },
    { value: 'additive', label: '外加剂' },
    { value: 'admixture', label: '掺合料' },
    { value: 'flyash', label: '粉煤灰' },
    { value: 'slag', label: '矿粉' },
    { value: 'silica', label: '硅灰' },
    { value: 'expansion', label: '膨胀剂' },
    { value: 'accelerator', label: '早强剂' },
    { value: 'ultrafine', label: '超细砂' }
  ];

  const filteredMaterials = materials.filter(m => m.stationId === currentStation);

  const handleAdd = () => {
    const newMaterial: Material = {
      id: Date.now().toString(),
      name: '',
      code: '',
      type: 'cement',
      specification: '',
      density: 0,
      sequence: filteredMaterials.length + 1,
      stationId: currentStation
    };
    setEditingId(newMaterial.id);
    setEditingMaterial(newMaterial);
    onMaterialsChange([...materials, newMaterial]);
  };

  const handleEdit = (material: Material) => {
    setEditingId(material.id);
    setEditingMaterial({ ...material });
  };

  const handleSave = () => {
    if (!editingId || !editingMaterial.name?.trim()) {
      toast({
        title: '保存失败',
        description: '请填写完整的材料信息',
        variant: 'destructive'
      });
      return;
    }

    const updatedMaterials = materials.map(m => 
      m.id === editingId ? { ...m, ...editingMaterial } as Material : m
    );
    
    onMaterialsChange(updatedMaterials);
    setEditingId(null);
    setEditingMaterial({});
    
    toast({
      title: '保存成功',
      description: '材料信息已更新'
    });
  };

  const handleCancel = () => {
    if (editingMaterial.name === '') {
      // 如果是新添加的空材料，删除它
      const updatedMaterials = materials.filter(m => m.id !== editingId);
      onMaterialsChange(updatedMaterials);
    }
    setEditingId(null);
    setEditingMaterial({});
  };

  const handleDelete = (id: string) => {
    const updatedMaterials = materials.filter(m => m.id !== id);
    onMaterialsChange(updatedMaterials);
    
    toast({
      title: '删除成功',
      description: '材料已删除'
    });
  };

  const handlePrint = () => {
    toast({
      title: '打印功能',
      description: '正在准备打印材料清单...'
    });
  };

  const handleSaveAll = () => {
    // 保存所有材料到服务器
    toast({
      title: '存盘成功',
      description: '所有材料信息已保存到数据库'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            材料名称管理
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 搅拌站选择 */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium">搅拌站:</span>
                <Select value={currentStation} onValueChange={setCurrentStation}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="选择搅拌站" />
                  </SelectTrigger>
                  <SelectContent>
                    {stations.map((station) => (
                      <SelectItem key={station.id} value={station.id}>
                        {station.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Badge variant="outline">
                  当前材料数量: {filteredMaterials.length}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex justify-between">
            <div className="flex space-x-2">
              <Button size="sm" onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" />
                增加
              </Button>
              <Button size="sm" variant="outline" onClick={handlePrint}>
                <Printer className="mr-2 h-4 w-4" />
                打印
              </Button>
              <Button size="sm" variant="outline" onClick={handleSaveAll}>
                <Save className="mr-2 h-4 w-4" />
                存盘
              </Button>
            </div>
          </div>

          {/* 材料表格 */}
          <div className="border rounded-lg overflow-hidden">
            <div className="max-h-96 overflow-auto">
              <Table>
                <TableHeader className="sticky top-0 bg-background">
                  <TableRow>
                    <TableHead className="w-16">ID</TableHead>
                    <TableHead className="w-32">料名</TableHead>
                    <TableHead className="w-20">序号</TableHead>
                    <TableHead className="w-24">类型</TableHead>
                    <TableHead className="w-32">规格</TableHead>
                    <TableHead className="w-24">密度</TableHead>
                    <TableHead className="w-20">代码</TableHead>
                    <TableHead className="w-32">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMaterials.map((material) => (
                    <TableRow key={material.id}>
                      <TableCell className="font-mono text-xs">
                        {material.id}
                      </TableCell>
                      
                      <TableCell>
                        {editingId === material.id ? (
                          <Input
                            value={editingMaterial.name || ''}
                            onChange={(e) => setEditingMaterial({
                              ...editingMaterial,
                              name: e.target.value
                            })}
                            className="h-8"
                            placeholder="材料名称"
                          />
                        ) : (
                          <span className="font-medium">{material.name}</span>
                        )}
                      </TableCell>
                      
                      <TableCell>
                        {editingId === material.id ? (
                          <Input
                            type="number"
                            value={editingMaterial.sequence || ''}
                            onChange={(e) => setEditingMaterial({
                              ...editingMaterial,
                              sequence: Number(e.target.value)
                            })}
                            className="h-8 w-16"
                          />
                        ) : (
                          material.sequence
                        )}
                      </TableCell>
                      
                      <TableCell>
                        {editingId === material.id ? (
                          <Select
                            value={editingMaterial.type || 'cement'}
                            onValueChange={(value: MaterialType) => setEditingMaterial({
                              ...editingMaterial,
                              type: value
                            })}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {materialTypes.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <Badge variant="secondary">
                            {materialTypes.find(t => t.value === material.type)?.label || material.type}
                          </Badge>
                        )}
                      </TableCell>
                      
                      <TableCell>
                        {editingId === material.id ? (
                          <Input
                            value={editingMaterial.specification || ''}
                            onChange={(e) => setEditingMaterial({
                              ...editingMaterial,
                              specification: e.target.value
                            })}
                            className="h-8"
                            placeholder="规格"
                          />
                        ) : (
                          material.specification
                        )}
                      </TableCell>
                      
                      <TableCell>
                        {editingId === material.id ? (
                          <Input
                            type="number"
                            value={editingMaterial.density || ''}
                            onChange={(e) => setEditingMaterial({
                              ...editingMaterial,
                              density: Number(e.target.value)
                            })}
                            className="h-8 w-20"
                            placeholder="密度"
                          />
                        ) : (
                          `${material.density} kg/m³`
                        )}
                      </TableCell>
                      
                      <TableCell>
                        {editingId === material.id ? (
                          <Input
                            value={editingMaterial.code || ''}
                            onChange={(e) => setEditingMaterial({
                              ...editingMaterial,
                              code: e.target.value
                            })}
                            className="h-8 w-16"
                            placeholder="代码"
                          />
                        ) : (
                          <code className="text-xs bg-muted px-1 py-0.5 rounded">
                            {material.code}
                          </code>
                        )}
                      </TableCell>
                      
                      <TableCell>
                        {editingId === material.id ? (
                          <div className="flex space-x-1">
                            <Button size="sm" onClick={handleSave}>
                              <Save className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={handleCancel}>
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <div className="flex space-x-1">
                            <Button size="sm" variant="outline" onClick={() => handleEdit(material)}>
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button 
                              size="sm" 
                              variant="destructive" 
                              onClick={() => handleDelete(material.id)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-4 gap-4">
            {materialTypes.slice(0, 4).map((type) => {
              const count = filteredMaterials.filter(m => m.type === type.value).length;
              return (
                <Card key={type.value}>
                  <CardContent className="p-3">
                    <div className="text-center">
                      <div className="text-lg font-bold text-primary">{count}</div>
                      <div className="text-xs text-muted-foreground">{type.label}</div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* 底部操作按钮 */}
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
