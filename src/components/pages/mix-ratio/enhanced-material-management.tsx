'use client';

import React, { useState, useMemo } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Database,
  Edit,
  Save,
  X,
  Plus,
  Trash2,
  Search,
  Download,
  Upload,
  RefreshCw,
  Package,
  Layers,
  Droplets,
  Zap,
  BarChart3,
  TrendingUp
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import type { Material, MixingStation, MaterialType } from '@/types/mixRatio';

interface MaterialUsageStats {
  materialId: string;
  usageCount: number;
  lastUsed: string;
  averageAmount: number;
  totalAmount: number;
}

interface EnhancedMaterialManagementProps {
  isOpen: boolean;
  onClose: () => void;
  selectedStation: string;
  stations: MixingStation[];
  materials: Material[];
  onMaterialsChange: (materials: Material[]) => void;
}

export function EnhancedMaterialManagement({
  isOpen,
  onClose,
  selectedStation,
  stations,
  materials,
  onMaterialsChange
}: EnhancedMaterialManagementProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingMaterial, setEditingMaterial] = useState<Partial<Material>>({});
  const [currentStation, setCurrentStation] = useState(selectedStation);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<MaterialType | 'all'>('all');
  const [activeTab, setActiveTab] = useState('management');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  const materialTypes: { value: MaterialType; label: string; icon: React.ReactNode }[] = [
    { value: 'cement', label: '水泥', icon: <Layers className="h-4 w-4" /> },
    { value: 'water', label: '水', icon: <Droplets className="h-4 w-4" /> },
    { value: 'sand', label: '砂', icon: <Package className="h-4 w-4" /> },
    { value: 'stone', label: '石子', icon: <Package className="h-4 w-4" /> },
    { value: 'additive', label: '外加剂', icon: <Zap className="h-4 w-4" /> },
    { value: 'admixture', label: '掺合料', icon: <Layers className="h-4 w-4" /> },
    { value: 'flyash', label: '粉煤灰', icon: <Layers className="h-4 w-4" /> },
    { value: 'slag', label: '矿粉', icon: <Layers className="h-4 w-4" /> },
  ];

  // 模拟材料使用统计数据
  const materialUsageStats = useMemo(() => {
    return materials.map(material => ({
      materialId: material.id,
      usageCount: Math.floor(Math.random() * 100) + 1,
      lastUsed: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      averageAmount: Math.round((Math.random() * 500 + 100) * 10) / 10,
      totalAmount: Math.round((Math.random() * 10000 + 1000) * 10) / 10
    }));
  }, [materials, isRefreshing]);

  const filteredMaterials = materials.filter(material => {
    const matchesStation = material.stationId === currentStation;
    const matchesSearch = searchTerm === '' || 
      material.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.specification.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || material.type === filterType;
    
    return matchesStation && matchesSearch && matchesType;
  });

  const handleEdit = (material: Material) => {
    setEditingId(material.id);
    setEditingMaterial(material);
  };

  const handleSave = () => {
    if (!editingId || !editingMaterial) return;

    const updatedMaterials = materials.map(m => 
      m.id === editingId ? { ...m, ...editingMaterial } : m
    );
    onMaterialsChange(updatedMaterials);
    setEditingId(null);
    setEditingMaterial({});
    
    toast({
      title: '保存成功',
      description: '材料信息已更新'
    });
  };

  const handleCancel = () => {
    setEditingId(null);
    setEditingMaterial({});
  };

  const handleAdd = () => {
    const newMaterial: Material = {
      id: `material-${Date.now()}`,
      name: '新材料',
      code: `M${String(filteredMaterials.length + 1).padStart(3, '0')}`,
      type: 'cement',
      specification: '',
      density: 2500,
      sequence: filteredMaterials.length + 1,
      stationId: currentStation
    };

    onMaterialsChange([...materials, newMaterial]);
    toast({
      title: '添加成功',
      description: '新材料已添加'
    });
  };

  const handleDelete = (id: string) => {
    const updatedMaterials = materials.filter(m => m.id !== id);
    onMaterialsChange(updatedMaterials);
    
    toast({
      title: '删除成功',
      description: '材料已删除'
    });
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
    toast({
      title: "刷新成功",
      description: "材料数据已更新"
    });
  };

  const handleExport = () => {
    const data = filteredMaterials.map(material => {
      const stats = materialUsageStats.find(s => s.materialId === material.id);
      return {
        材料名称: material.name,
        材料编码: material.code,
        材料类型: materialTypes.find(t => t.value === material.type)?.label,
        规格: material.specification,
        密度: material.density + ' kg/m³',
        使用次数: stats?.usageCount || 0,
        最后使用: stats?.lastUsed || '-',
        平均用量: stats?.averageAmount + ' kg' || '-'
      };
    });
    
    console.log('导出数据:', data);
    toast({
      title: "导出成功",
      description: `已导出 ${data.length} 条材料数据`
    });
  };

  const handleImport = () => {
    // 模拟导入功能
    toast({
      title: "导入功能",
      description: "请选择要导入的材料数据文件"
    });
  };

  const getMaterialTypeInfo = (type: MaterialType) => {
    return materialTypes.find(t => t.value === type) || materialTypes[0];
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              材料管理
            </span>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                刷新
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleImport}
              >
                <Upload className="h-4 w-4 mr-1" />
                导入
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
              >
                <Download className="h-4 w-4 mr-1" />
                导出
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="management">材料管理</TabsTrigger>
            <TabsTrigger value="usage">使用统计</TabsTrigger>
            <TabsTrigger value="analysis">数据分析</TabsTrigger>
          </TabsList>

          <TabsContent value="management" className="space-y-4 mt-4">
            {/* 筛选和搜索 */}
            <Card>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">搅拌站</Label>
                    <Select value={currentStation} onValueChange={setCurrentStation}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择搅拌站" />
                      </SelectTrigger>
                      <SelectContent>
                        {stations.map((station) => (
                          <SelectItem key={station.id} value={station.id}>
                            {station.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">搜索</Label>
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="搜索材料名称、编码..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">材料类型</Label>
                    <Select value={filterType} onValueChange={(value) => setFilterType(value as any)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部类型</SelectItem>
                        {materialTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center space-x-2">
                              {type.icon}
                              <span>{type.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex justify-between items-center mt-4">
                  <div className="flex items-center space-x-4">
                    <div className="text-sm text-muted-foreground">
                      共 {filteredMaterials.length} 种材料
                    </div>
                    {materialTypes.map((type) => {
                      const count = filteredMaterials.filter(m => m.type === type.value).length;
                      if (count === 0) return null;
                      return (
                        <Badge key={type.value} variant="outline" className="text-xs">
                          {type.label}: {count}
                        </Badge>
                      );
                    })}
                  </div>
                  <Button onClick={handleAdd}>
                    <Plus className="mr-2 h-4 w-4" />
                    添加材料
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 材料列表 */}
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>材料名称</TableHead>
                      <TableHead>材料编码</TableHead>
                      <TableHead>材料类型</TableHead>
                      <TableHead>规格</TableHead>
                      <TableHead>密度 (kg/m³)</TableHead>
                      <TableHead>显示顺序</TableHead>
                      <TableHead>使用频率</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMaterials.map((material) => {
                      const isEditing = editingId === material.id;
                      const stats = materialUsageStats.find(s => s.materialId === material.id);
                      const typeInfo = getMaterialTypeInfo(material.type);

                      return (
                        <TableRow key={material.id}>
                          <TableCell>
                            {isEditing ? (
                              <Input
                                value={editingMaterial.name || ''}
                                onChange={(e) => setEditingMaterial({...editingMaterial, name: e.target.value})}
                                className="h-8"
                              />
                            ) : (
                              <div className="flex items-center space-x-2">
                                {typeInfo.icon}
                                <span>{material.name}</span>
                              </div>
                            )}
                          </TableCell>

                          <TableCell>
                            {isEditing ? (
                              <Input
                                value={editingMaterial.code || ''}
                                onChange={(e) => setEditingMaterial({...editingMaterial, code: e.target.value})}
                                className="h-8"
                              />
                            ) : (
                              <Badge variant="outline">{material.code}</Badge>
                            )}
                          </TableCell>

                          <TableCell>
                            {isEditing ? (
                              <Select
                                value={editingMaterial.type || material.type}
                                onValueChange={(value) => setEditingMaterial({...editingMaterial, type: value as MaterialType})}
                              >
                                <SelectTrigger className="h-8">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {materialTypes.map((type) => (
                                    <SelectItem key={type.value} value={type.value}>
                                      <div className="flex items-center space-x-2">
                                        {type.icon}
                                        <span>{type.label}</span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            ) : (
                              <Badge>{typeInfo.label}</Badge>
                            )}
                          </TableCell>

                          <TableCell>
                            {isEditing ? (
                              <Input
                                value={editingMaterial.specification || ''}
                                onChange={(e) => setEditingMaterial({...editingMaterial, specification: e.target.value})}
                                className="h-8"
                              />
                            ) : (
                              material.specification
                            )}
                          </TableCell>

                          <TableCell>
                            {isEditing ? (
                              <Input
                                type="number"
                                value={editingMaterial.density || ''}
                                onChange={(e) => setEditingMaterial({...editingMaterial, density: Number(e.target.value)})}
                                className="h-8"
                              />
                            ) : (
                              material.density
                            )}
                          </TableCell>

                          <TableCell>
                            {isEditing ? (
                              <Input
                                type="number"
                                value={editingMaterial.sequence || ''}
                                onChange={(e) => setEditingMaterial({...editingMaterial, sequence: Number(e.target.value)})}
                                className="h-8"
                              />
                            ) : (
                              material.sequence
                            )}
                          </TableCell>

                          <TableCell>
                            {stats && (
                              <div className="text-sm">
                                <div className="font-medium">{stats.usageCount} 次</div>
                                <div className="text-muted-foreground text-xs">
                                  最后使用: {stats.lastUsed}
                                </div>
                              </div>
                            )}
                          </TableCell>

                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {isEditing ? (
                                <>
                                  <Button size="sm" onClick={handleSave}>
                                    <Save className="h-4 w-4" />
                                  </Button>
                                  <Button size="sm" variant="outline" onClick={handleCancel}>
                                    <X className="h-4 w-4" />
                                  </Button>
                                </>
                              ) : (
                                <>
                                  <Button size="sm" variant="outline" onClick={() => handleEdit(material)}>
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button size="sm" variant="destructive" onClick={() => handleDelete(material.id)}>
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="usage" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredMaterials.map((material) => {
                const stats = materialUsageStats.find(s => s.materialId === material.id);
                const typeInfo = getMaterialTypeInfo(material.type);

                return (
                  <Card key={material.id}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center space-x-2">
                        {typeInfo.icon}
                        <span>{material.name}</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">使用次数:</span>
                          <div className="font-medium">{stats?.usageCount || 0}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">平均用量:</span>
                          <div className="font-medium">{stats?.averageAmount || 0} kg</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">总用量:</span>
                          <div className="font-medium">{stats?.totalAmount || 0} kg</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">最后使用:</span>
                          <div className="font-medium text-xs">{stats?.lastUsed || '-'}</div>
                        </div>
                      </div>

                      <Separator />

                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span>使用频率</span>
                          <span>{Math.min(100, (stats?.usageCount || 0) * 2)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${Math.min(100, (stats?.usageCount || 0) * 2)}%` }}
                          ></div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="analysis" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">总材料数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{filteredMaterials.length}</div>
                  <div className="text-xs text-muted-foreground">
                    {materialTypes.length} 个类型
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">最常用材料</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-sm font-medium">
                    {materialUsageStats.length > 0 &&
                      materials.find(m => m.id === materialUsageStats.sort((a, b) => b.usageCount - a.usageCount)[0]?.materialId)?.name
                    }
                  </div>
                  <div className="text-xs text-muted-foreground">
                    使用 {materialUsageStats.length > 0 ? materialUsageStats.sort((a, b) => b.usageCount - a.usageCount)[0]?.usageCount : 0} 次
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">总用量</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Math.round(materialUsageStats.reduce((sum, s) => sum + s.totalAmount, 0))} kg
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <TrendingUp className="inline h-3 w-3 mr-1" />
                    较上月 +12%
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">平均密度</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Math.round(filteredMaterials.reduce((sum, m) => sum + m.density, 0) / filteredMaterials.length)} kg/m³
                  </div>
                  <div className="text-xs text-muted-foreground">
                    所有材料平均
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">材料类型分布</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {materialTypes.map((type) => {
                    const count = filteredMaterials.filter(m => m.type === type.value).length;
                    const percentage = filteredMaterials.length > 0 ? (count / filteredMaterials.length) * 100 : 0;

                    return (
                      <div key={type.value} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <div className="flex items-center space-x-2">
                            {type.icon}
                            <span>{type.label}</span>
                          </div>
                          <span>{count} 种 ({Math.round(percentage)}%)</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-600 h-2 rounded-full"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
