'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Calculator, 
  RotateCcw, 
  CheckCircle, 
  Plus, 
  Minus,
  BookOpen,
  Zap
} from 'lucide-react';
import type { MixCalculationParams, CalculationMethod } from '@/types/mixRatio';

interface ModernCalculationTableProps {
  params: MixCalculationParams;
  calculationMethod: CalculationMethod;
  onParamsChange: (params: Partial<MixCalculationParams>) => void;
  onMethodChange: (method: CalculationMethod) => void;
  onCalculate: () => void;
  onApply: () => void;
  onOpenCalculationBook: () => void;
  isCalculating?: boolean;
}

export function ModernCalculationTable({
  params,
  calculationMethod,
  onParamsChange,
  onMethodChange,
  onCalculate,
  onApply,
  onOpenCalculationBook,
  isCalculating = false
}: ModernCalculationTableProps) {
  const [distributionRatio, setDistributionRatio] = useState<Record<string, number>>({});

  const handleParamChange = (field: keyof MixCalculationParams, value: number) => {
    onParamsChange({ [field]: value });
  };

  const adjustValue = (field: keyof MixCalculationParams, delta: number) => {
    const currentValue = params[field];
    const newValue = Math.max(0, currentValue + delta);
    handleParamChange(field, newValue);
  };

  const calculationMethods = [
    { value: 'no_flyash_excess', label: '不考虑粉煤灰超量系数' },
    { value: 'consider_flyash', label: '考虑粉煤灰系数' },
    { value: 'flyash_volume_conversion', label: '考虑粉煤灰系数且体积折算' },
    { value: 'excess_flyash', label: '超量煤灰' }
  ];

  const NumberInput = ({ 
    label, 
    value, 
    field, 
    unit, 
    step = 0.01, 
    min = 0 
  }: { 
    label: string; 
    value: number; 
    field: keyof MixCalculationParams; 
    unit: string; 
    step?: number; 
    min?: number; 
  }) => (
    <div className="space-y-1">
      <Label className="text-xs text-muted-foreground">{label}</Label>
      <div className="flex items-center space-x-1">
        <Button
          variant="outline"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={() => adjustValue(field, -step)}
          disabled={value <= min}
        >
          <Minus className="h-3 w-3" />
        </Button>
        <Input
          type="number"
          step={step}
          min={min}
          value={value}
          onChange={(e) => handleParamChange(field, Number(e.target.value))}
          className="h-6 text-xs text-center flex-1"
        />
        <Button
          variant="outline"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={() => adjustValue(field, step)}
        >
          <Plus className="h-3 w-3" />
        </Button>
        <span className="text-xs text-muted-foreground w-12">{unit}</span>
      </div>
    </div>
  );

  return (
    <Card className="border-l-4 border-l-green-500">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-green-700 flex items-center justify-between">
          <span>计算参数</span>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onOpenCalculationBook}
              className="h-6 text-xs"
            >
              <BookOpen className="mr-1 h-3 w-3" />
              配比计算书
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0 space-y-4">
        {/* 基础参数行 */}
        <div className="grid grid-cols-3 gap-3">
          <NumberInput
            label="密度"
            value={params.density}
            field="density"
            unit="t/m³"
            step={0.01}
          />
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">总重量</Label>
            <div className="flex items-center space-x-1">
              <Input
                type="number"
                value={params.totalWeight}
                readOnly
                className="h-6 text-xs bg-muted"
              />
              <span className="text-xs text-muted-foreground w-12">kg</span>
            </div>
          </div>
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">搅拌时间</Label>
            <div className="flex items-center space-x-1">
              <Input
                type="number"
                value={params.mixingTime}
                readOnly
                className="h-6 text-xs bg-muted"
              />
              <span className="text-xs text-muted-foreground w-12">秒</span>
            </div>
          </div>
        </div>

        <Separator />

        {/* 配比参数表格 */}
        <div className="space-y-3">
          <div className="text-xs font-medium text-muted-foreground">配比参数</div>
          
          {/* 第一行：基础参数 */}
          <div className="grid grid-cols-4 gap-2">
            <NumberInput
              label="容重"
              value={params.density}
              field="density"
              unit="t/m³"
              step={0.01}
            />
            <NumberInput
              label="水胶比"
              value={params.waterCementRatio}
              field="waterCementRatio"
              unit=""
              step={0.01}
            />
            <NumberInput
              label="用水量"
              value={params.waterContent}
              field="waterContent"
              unit="kg"
              step={1}
            />
            <NumberInput
              label="沙率"
              value={params.sandRatio}
              field="sandRatio"
              unit="%"
              step={0.5}
            />
          </div>

          {/* 第二行：外加剂参数 */}
          <div className="grid grid-cols-4 gap-2">
            <NumberInput
              label="外加剂%"
              value={params.additiveRatio}
              field="additiveRatio"
              unit="%"
              step={0.1}
            />
            <NumberInput
              label="防冻剂%"
              value={params.antifreezeRatio}
              field="antifreezeRatio"
              unit="%"
              step={0.1}
            />
            <NumberInput
              label="粉煤灰%"
              value={params.flyashRatio}
              field="flyashRatio"
              unit="%"
              step={0.5}
            />
            <NumberInput
              label="矿粉%"
              value={params.slagRatio}
              field="slagRatio"
              unit="%"
              step={0.5}
            />
          </div>

          {/* 第三行：其他参数 */}
          <div className="grid grid-cols-4 gap-2">
            <NumberInput
              label="矿S105%"
              value={params.silicaRatio}
              field="silicaRatio"
              unit="%"
              step={0.1}
            />
            <NumberInput
              label="膨胀剂%"
              value={params.expansionRatio}
              field="expansionRatio"
              unit="%"
              step={0.1}
            />
            <NumberInput
              label="早强剂%"
              value={params.acceleratorRatio}
              field="acceleratorRatio"
              unit="%"
              step={0.1}
            />
            <NumberInput
              label="超细砂%"
              value={params.ultrafineRatio}
              field="ultrafineRatio"
              unit="%"
              step={0.5}
            />
          </div>
        </div>

        <Separator />

        {/* 计算方法和系数 */}
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">计算方法</Label>
              <Select value={calculationMethod} onValueChange={onMethodChange}>
                <SelectTrigger className="h-6 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {calculationMethods.map((method) => (
                    <SelectItem key={method.value} value={method.value}>
                      {method.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">超量取代系数</Label>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  step="0.01"
                  defaultValue="1.0"
                  className="h-6 text-xs"
                />
                <span className="text-xs text-muted-foreground">粉煤灰</span>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* 操作按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              onClick={onCalculate}
              disabled={isCalculating}
              size="sm"
              className="h-7"
            >
              {isCalculating ? (
                <RotateCcw className="mr-1 h-3 w-3 animate-spin" />
              ) : (
                <Calculator className="mr-1 h-3 w-3" />
              )}
              反算
            </Button>
            
            <Button
              onClick={onApply}
              variant="outline"
              size="sm"
              className="h-7"
            >
              <CheckCircle className="mr-1 h-3 w-3" />
              应用
            </Button>
          </div>

          <div className="text-xs text-muted-foreground">
            水泥取代率: <Badge variant="outline">0.85</Badge>
            沙率: <Badge variant="outline">{params.sandRatio}%</Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
