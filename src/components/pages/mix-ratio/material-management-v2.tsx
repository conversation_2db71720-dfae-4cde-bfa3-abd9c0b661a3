'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Trash2, Edit, Save, X, Search, Download, Upload } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Material {
  id: string;
  name: string;
  code: string;
  type: 'aggregate' | 'powder' | 'water' | 'additive';
  specification: string;
  density: number;
  supplier: string;
  price: number;
  unit: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface MaterialManagementProps {
  onClose: () => void;
  onMaterialsChange?: (materials: Material[]) => void;
}

export function MaterialManagementV2({ onClose, onMaterialsChange }: MaterialManagementProps) {
  const [materials, setMaterials] = useState<Material[]>([
    {
      id: '1',
      name: 'P.O 42.5水泥',
      code: 'C001',
      type: 'powder',
      specification: 'P.O 42.5',
      density: 3.1,
      supplier: '海螺水泥',
      price: 420,
      unit: 't',
      isActive: true,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-15'
    },
    {
      id: '2',
      name: '机制砂',
      code: 'S001',
      type: 'aggregate',
      specification: '中砂 2.3-3.0',
      density: 2.65,
      supplier: '本地砂场',
      price: 85,
      unit: 't',
      isActive: true,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-10'
    },
    {
      id: '3',
      name: '碎石',
      code: 'G001',
      type: 'aggregate',
      specification: '5-25mm连续级配',
      density: 2.7,
      supplier: '本地石场',
      price: 75,
      unit: 't',
      isActive: true,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-12'
    },
    {
      id: '4',
      name: '聚羧酸减水剂',
      code: 'A001',
      type: 'additive',
      specification: '高效减水剂',
      density: 1.05,
      supplier: '建研院',
      price: 2800,
      unit: 't',
      isActive: true,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-08'
    },
    {
      id: '5',
      name: 'I级粉煤灰',
      code: 'F001',
      type: 'powder',
      specification: 'I级',
      density: 2.2,
      supplier: '电厂',
      price: 180,
      unit: 't',
      isActive: true,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-05'
    }
  ]);

  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingMaterial, setEditingMaterial] = useState<Partial<Material>>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const { toast } = useToast();

  const materialTypes = [
    { value: 'powder', label: '粉料', color: 'bg-orange-100 text-orange-800' },
    { value: 'aggregate', label: '骨料', color: 'bg-gray-100 text-gray-800' },
    { value: 'water', label: '水', color: 'bg-blue-100 text-blue-800' },
    { value: 'additive', label: '外加剂', color: 'bg-green-100 text-green-800' }
  ];

  const filteredMaterials = materials.filter(material => {
    const matchesSearch = material.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         material.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         material.supplier.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || material.type === filterType;
    return matchesSearch && matchesType;
  });

  const handleAdd = () => {
    const newMaterial: Material = {
      id: Date.now().toString(),
      name: '',
      code: '',
      type: 'powder',
      specification: '',
      density: 0,
      supplier: '',
      price: 0,
      unit: 't',
      isActive: true,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0]
    };
    setEditingId(newMaterial.id);
    setEditingMaterial(newMaterial);
    setMaterials(prev => [...prev, newMaterial]);
  };

  const handleEdit = (material: Material) => {
    setEditingId(material.id);
    setEditingMaterial({ ...material });
  };

  const handleSave = () => {
    if (!editingId || !editingMaterial.name?.trim()) {
      toast({
        title: '保存失败',
        description: '请填写完整的材料信息',
        variant: 'destructive'
      });
      return;
    }

    const updatedMaterials = materials.map(m => 
      m.id === editingId ? { 
        ...m, 
        ...editingMaterial,
        updatedAt: new Date().toISOString().split('T')[0]
      } as Material : m
    );
    
    setMaterials(updatedMaterials);
    setEditingId(null);
    setEditingMaterial({});
    onMaterialsChange?.(updatedMaterials);
    
    toast({
      title: '保存成功',
      description: '材料信息已更新'
    });
  };

  const handleCancel = () => {
    if (editingMaterial.name === '') {
      const updatedMaterials = materials.filter(m => m.id !== editingId);
      setMaterials(updatedMaterials);
    }
    setEditingId(null);
    setEditingMaterial({});
  };

  const handleDelete = (id: string) => {
    const updatedMaterials = materials.filter(m => m.id !== id);
    setMaterials(updatedMaterials);
    onMaterialsChange?.(updatedMaterials);
    
    toast({
      title: '删除成功',
      description: '材料已删除'
    });
  };

  const handleToggleActive = (id: string, isActive: boolean) => {
    const updatedMaterials = materials.map(m => 
      m.id === id ? { ...m, isActive, updatedAt: new Date().toISOString().split('T')[0] } : m
    );
    setMaterials(updatedMaterials);
    onMaterialsChange?.(updatedMaterials);
  };

  const handleExport = () => {
    const dataStr = JSON.stringify(materials, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `materials_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
    
    toast({
      title: '导出成功',
      description: '材料数据已导出'
    });
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedMaterials = JSON.parse(e.target?.result as string);
        setMaterials(importedMaterials);
        onMaterialsChange?.(importedMaterials);
        toast({
          title: '导入成功',
          description: '材料数据已导入'
        });
      } catch (error) {
        toast({
          title: '导入失败',
          description: '文件格式错误',
          variant: 'destructive'
        });
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="space-y-4">
      <Tabs defaultValue="list" className="space-y-4">
        <TabsList>
          <TabsTrigger value="list">材料列表</TabsTrigger>
          <TabsTrigger value="stats">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          {/* 搜索和过滤 */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between space-x-4">
                <div className="flex items-center space-x-4 flex-1">
                  <div className="relative flex-1 max-w-sm">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索材料名称、代码或供应商..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                  
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      {materialTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex space-x-2">
                  <Button size="sm" onClick={handleAdd}>
                    <Plus className="mr-2 h-4 w-4" />
                    新增材料
                  </Button>
                  <Button size="sm" variant="outline" onClick={handleExport}>
                    <Download className="mr-2 h-4 w-4" />
                    导出
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => document.getElementById('import-file')?.click()}>
                    <Upload className="mr-2 h-4 w-4" />
                    导入
                  </Button>
                  <input
                    id="import-file"
                    type="file"
                    accept=".json"
                    onChange={handleImport}
                    className="hidden"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 材料表格 */}
          <Card>
            <CardContent className="p-0">
              <div className="max-h-96 overflow-auto">
                <Table>
                  <TableHeader className="sticky top-0 bg-background">
                    <TableRow>
                      <TableHead className="w-16">状态</TableHead>
                      <TableHead>材料名称</TableHead>
                      <TableHead>代码</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>规格</TableHead>
                      <TableHead>密度</TableHead>
                      <TableHead>供应商</TableHead>
                      <TableHead>价格</TableHead>
                      <TableHead>更新时间</TableHead>
                      <TableHead className="w-32">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMaterials.map((material) => (
                      <TableRow key={material.id} className={!material.isActive ? 'opacity-60' : ''}>
                        <TableCell>
                          <Badge variant={material.isActive ? 'default' : 'secondary'}>
                            {material.isActive ? '启用' : '禁用'}
                          </Badge>
                        </TableCell>
                        
                        <TableCell>
                          {editingId === material.id ? (
                            <Input
                              value={editingMaterial.name || ''}
                              onChange={(e) => setEditingMaterial({
                                ...editingMaterial,
                                name: e.target.value
                              })}
                              className="h-8"
                              placeholder="材料名称"
                            />
                          ) : (
                            <span className="font-medium">{material.name}</span>
                          )}
                        </TableCell>
                        
                        <TableCell>
                          {editingId === material.id ? (
                            <Input
                              value={editingMaterial.code || ''}
                              onChange={(e) => setEditingMaterial({
                                ...editingMaterial,
                                code: e.target.value
                              })}
                              className="h-8 w-20"
                              placeholder="代码"
                            />
                          ) : (
                            <code className="text-xs bg-muted px-1 py-0.5 rounded">
                              {material.code}
                            </code>
                          )}
                        </TableCell>
                        
                        <TableCell>
                          {editingId === material.id ? (
                            <Select
                              value={editingMaterial.type || material.type}
                              onValueChange={(value: any) => setEditingMaterial({
                                ...editingMaterial,
                                type: value
                              })}
                            >
                              <SelectTrigger className="h-8 w-24">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {materialTypes.map((type) => (
                                  <SelectItem key={type.value} value={type.value}>
                                    {type.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          ) : (
                            <Badge className={materialTypes.find(t => t.value === material.type)?.color}>
                              {materialTypes.find(t => t.value === material.type)?.label}
                            </Badge>
                          )}
                        </TableCell>
                        
                        <TableCell>
                          {editingId === material.id ? (
                            <Input
                              value={editingMaterial.specification || ''}
                              onChange={(e) => setEditingMaterial({
                                ...editingMaterial,
                                specification: e.target.value
                              })}
                              className="h-8"
                              placeholder="规格"
                            />
                          ) : (
                            material.specification
                          )}
                        </TableCell>
                        
                        <TableCell>
                          {editingId === material.id ? (
                            <Input
                              type="number"
                              step="0.01"
                              value={editingMaterial.density || ''}
                              onChange={(e) => setEditingMaterial({
                                ...editingMaterial,
                                density: Number(e.target.value)
                              })}
                              className="h-8 w-20"
                              placeholder="密度"
                            />
                          ) : (
                            `${material.density} t/m³`
                          )}
                        </TableCell>
                        
                        <TableCell>
                          {editingId === material.id ? (
                            <Input
                              value={editingMaterial.supplier || ''}
                              onChange={(e) => setEditingMaterial({
                                ...editingMaterial,
                                supplier: e.target.value
                              })}
                              className="h-8"
                              placeholder="供应商"
                            />
                          ) : (
                            material.supplier
                          )}
                        </TableCell>
                        
                        <TableCell>
                          {editingId === material.id ? (
                            <Input
                              type="number"
                              value={editingMaterial.price || ''}
                              onChange={(e) => setEditingMaterial({
                                ...editingMaterial,
                                price: Number(e.target.value)
                              })}
                              className="h-8 w-20"
                              placeholder="价格"
                            />
                          ) : (
                            `¥${material.price}/${material.unit}`
                          )}
                        </TableCell>
                        
                        <TableCell className="text-xs text-muted-foreground">
                          {material.updatedAt}
                        </TableCell>
                        
                        <TableCell>
                          {editingId === material.id ? (
                            <div className="flex space-x-1">
                              <Button size="sm" onClick={handleSave}>
                                <Save className="h-3 w-3" />
                              </Button>
                              <Button size="sm" variant="outline" onClick={handleCancel}>
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ) : (
                            <div className="flex space-x-1">
                              <Button size="sm" variant="outline" onClick={() => handleEdit(material)}>
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button 
                                size="sm" 
                                variant={material.isActive ? 'secondary' : 'default'}
                                onClick={() => handleToggleActive(material.id, !material.isActive)}
                              >
                                {material.isActive ? '禁用' : '启用'}
                              </Button>
                              <Button 
                                size="sm" 
                                variant="destructive" 
                                onClick={() => handleDelete(material.id)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats" className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {materialTypes.map((type) => {
              const count = materials.filter(m => m.type === type.value).length;
              const activeCount = materials.filter(m => m.type === type.value && m.isActive).length;
              return (
                <Card key={type.value}>
                  <CardContent className="p-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">{activeCount}/{count}</div>
                      <div className="text-sm text-muted-foreground">{type.label}</div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>价格统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {materialTypes.map((type) => {
                  const typeMaterials = materials.filter(m => m.type === type.value && m.isActive);
                  const avgPrice = typeMaterials.length > 0 
                    ? typeMaterials.reduce((sum, m) => sum + m.price, 0) / typeMaterials.length 
                    : 0;
                  return (
                    <div key={type.value} className="flex justify-between items-center">
                      <Badge className={type.color}>{type.label}</Badge>
                      <span className="text-sm">平均价格: ¥{avgPrice.toFixed(0)}/t</span>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
