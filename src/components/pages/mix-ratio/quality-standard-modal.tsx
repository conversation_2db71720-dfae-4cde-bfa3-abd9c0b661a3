'use client';

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CheckSquare, Save, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

import type { QualityStandard } from '@/types/mixRatio';

interface QualityStandardModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function QualityStandardModal({ isOpen, onClose }: QualityStandardModalProps) {
  const [selectedStandard, setSelectedStandard] = useState<string>('');
  const [standards, setStandards] = useState<QualityStandard[]>([]);
  const [currentStandard, setCurrentStandard] = useState<QualityStandard | null>(null);
  const { toast } = useToast();

  React.useEffect(() => {
    if (isOpen) {
      loadStandards();
    }
  }, [isOpen]);

  const loadStandards = () => {
    // 模拟加载质量标准数据
    const mockStandards: QualityStandard[] = [
      {
        id: '1',
        name: 'GB/T 14902-2012 预拌混凝土',
        standards: {
          slump: { min: 160, max: 200, unit: 'mm' },
          strength: { min: 25, max: 35, unit: 'MPa' },
          waterCementRatio: { min: 0.4, max: 0.6, unit: '' },
          chlorideContent: { max: 0.3, unit: '%' },
          alkaliContent: { max: 3.0, unit: 'kg/m³' }
        },
        isActive: true
      },
      {
        id: '2',
        name: 'JGJ 55-2011 普通混凝土配合比设计规程',
        standards: {
          slump: { min: 150, max: 220, unit: 'mm' },
          strength: { min: 20, max: 40, unit: 'MPa' },
          waterCementRatio: { min: 0.35, max: 0.65, unit: '' },
          chlorideContent: { max: 0.2, unit: '%' },
          alkaliContent: { max: 2.5, unit: 'kg/m³' }
        },
        isActive: false
      }
    ];
    
    setStandards(mockStandards);
    if (mockStandards.length > 0) {
      setSelectedStandard(mockStandards[0].id);
      setCurrentStandard(mockStandards[0]);
    }
  };

  const handleStandardChange = (standardId: string) => {
    setSelectedStandard(standardId);
    const standard = standards.find(s => s.id === standardId);
    setCurrentStandard(standard || null);
  };

  const handleSave = () => {
    if (!currentStandard) return;
    
    // 保存质量标准设置
    toast({
      title: '保存成功',
      description: '质量标准设置已保存'
    });
  };

  const handleApply = () => {
    if (!currentStandard) return;
    
    // 应用质量标准到当前配比
    toast({
      title: '应用成功',
      description: '质量标准已应用到当前配比'
    });
    onClose();
  };

  const updateStandardValue = (key: string, field: string, value: number) => {
    if (!currentStandard) return;
    
    setCurrentStandard({
      ...currentStandard,
      standards: {
        ...currentStandard.standards,
        [key]: {
          ...currentStandard.standards[key],
          [field]: value
        }
      }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5" />
            检测标准设置
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 标准选择 */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                <Label className="text-sm font-medium">选择标准:</Label>
                <Select value={selectedStandard} onValueChange={handleStandardChange}>
                  <SelectTrigger className="w-64">
                    <SelectValue placeholder="选择质量标准" />
                  </SelectTrigger>
                  <SelectContent>
                    {standards.map((standard) => (
                      <SelectItem key={standard.id} value={standard.id}>
                        {standard.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {currentStandard && (
                  <div className="text-sm text-muted-foreground">
                    状态: {currentStandard.isActive ? '启用' : '禁用'}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 标准参数设置 */}
          {currentStandard && (
            <Tabs defaultValue="basic" className="space-y-4">
              <TabsList>
                <TabsTrigger value="basic">基础参数</TabsTrigger>
                <TabsTrigger value="chemical">化学指标</TabsTrigger>
                <TabsTrigger value="physical">物理性能</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">基础参数标准</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>参数名称</TableHead>
                            <TableHead>最小值</TableHead>
                            <TableHead>最大值</TableHead>
                            <TableHead>单位</TableHead>
                            <TableHead>当前值</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow>
                            <TableCell className="font-medium">坍落度</TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                value={currentStandard.standards.slump?.min || ''}
                                onChange={(e) => updateStandardValue('slump', 'min', Number(e.target.value))}
                                className="h-8 w-20"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                value={currentStandard.standards.slump?.max || ''}
                                onChange={(e) => updateStandardValue('slump', 'max', Number(e.target.value))}
                                className="h-8 w-20"
                              />
                            </TableCell>
                            <TableCell>mm</TableCell>
                            <TableCell className="text-green-600 font-medium">180</TableCell>
                          </TableRow>
                          
                          <TableRow>
                            <TableCell className="font-medium">强度等级</TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                value={currentStandard.standards.strength?.min || ''}
                                onChange={(e) => updateStandardValue('strength', 'min', Number(e.target.value))}
                                className="h-8 w-20"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                value={currentStandard.standards.strength?.max || ''}
                                onChange={(e) => updateStandardValue('strength', 'max', Number(e.target.value))}
                                className="h-8 w-20"
                              />
                            </TableCell>
                            <TableCell>MPa</TableCell>
                            <TableCell className="text-green-600 font-medium">C30</TableCell>
                          </TableRow>
                          
                          <TableRow>
                            <TableCell className="font-medium">水胶比</TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                step="0.01"
                                value={currentStandard.standards.waterCementRatio?.min || ''}
                                onChange={(e) => updateStandardValue('waterCementRatio', 'min', Number(e.target.value))}
                                className="h-8 w-20"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                step="0.01"
                                value={currentStandard.standards.waterCementRatio?.max || ''}
                                onChange={(e) => updateStandardValue('waterCementRatio', 'max', Number(e.target.value))}
                                className="h-8 w-20"
                              />
                            </TableCell>
                            <TableCell>-</TableCell>
                            <TableCell className="text-green-600 font-medium">0.45</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="chemical" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">化学指标标准</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>指标名称</TableHead>
                          <TableHead>最大值</TableHead>
                          <TableHead>单位</TableHead>
                          <TableHead>当前值</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow>
                          <TableCell className="font-medium">氯离子含量</TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              step="0.01"
                              value={currentStandard.standards.chlorideContent?.max || ''}
                              onChange={(e) => updateStandardValue('chlorideContent', 'max', Number(e.target.value))}
                              className="h-8 w-20"
                            />
                          </TableCell>
                          <TableCell>%</TableCell>
                          <TableCell className="text-green-600 font-medium">0.15</TableCell>
                        </TableRow>
                        
                        <TableRow>
                          <TableCell className="font-medium">碱含量</TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              step="0.1"
                              value={currentStandard.standards.alkaliContent?.max || ''}
                              onChange={(e) => updateStandardValue('alkaliContent', 'max', Number(e.target.value))}
                              className="h-8 w-20"
                            />
                          </TableCell>
                          <TableCell>kg/m³</TableCell>
                          <TableCell className="text-green-600 font-medium">2.1</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="physical" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">物理性能标准</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center text-muted-foreground py-8">
                      物理性能标准设置功能开发中...
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          )}
        </div>

        {/* 底部操作按钮 */}
        <div className="flex justify-between pt-4 border-t">
          <div className="flex space-x-2">
            <Button variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              重置为默认
            </Button>
          </div>
          
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleSave}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
            <Button onClick={handleApply}>
              应用到当前配比
            </Button>
            <Button variant="outline" onClick={onClose}>
              关闭
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
