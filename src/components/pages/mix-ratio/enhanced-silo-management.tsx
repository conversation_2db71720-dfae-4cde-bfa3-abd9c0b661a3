'use client';

import React, { useState, useMemo } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  <PERSON>house,
  Edit,
  Save,
  X,
  CheckCircle,
  XCircle,
  Plus,
  Trash2,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Search,
  Download,
  RefreshCw,
  BarChart3
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import type { SiloInfo, MixingStation, MaterialType } from '@/types/mixRatio';

interface SiloStatus {
  id: string;
  capacity: number;
  current: number;
  reserved: number;
  available: number;
  utilizationRate: number;
  status: 'normal' | 'warning' | 'critical' | 'empty';
  lastUpdated: string;
}

interface EnhancedSiloManagementProps {
  isOpen: boolean;
  onClose: () => void;
  selectedStation: string;
  stations: MixingStation[];
  silos: SiloInfo[];
  onSilosChange: (silos: SiloInfo[]) => void;
}

export function EnhancedSiloManagement({
  isOpen,
  onClose,
  selectedStation,
  stations,
  silos,
  onSilosChange
}: EnhancedSiloManagementProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingSilo, setEditingSilo] = useState<Partial<SiloInfo>>({});
  const [currentStation, setCurrentStation] = useState(selectedStation);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<MaterialType | 'all'>('all');
  const [filterStatus, setFilterStatus] = useState<'all' | 'normal' | 'warning' | 'critical'>('all');
  const [activeTab, setActiveTab] = useState('management');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  const materialTypes: { value: MaterialType; label: string }[] = [
    { value: 'cement', label: '水泥' },
    { value: 'water', label: '水' },
    { value: 'sand', label: '砂' },
    { value: 'stone', label: '石子' },
    { value: 'additive', label: '外加剂' },
    { value: 'admixture', label: '掺合料' },
    { value: 'flyash', label: '粉煤灰' },
    { value: 'slag', label: '矿粉' },
  ];

  // 模拟料仓状态数据
  const siloStatuses = useMemo(() => {
    return silos.map(silo => {
      const capacity = getSiloCapacityByType(silo.materialType);
      const current = Math.random() * capacity * 0.8 + capacity * 0.1;
      const reserved = Math.random() * current * 0.2;
      const available = current - reserved;
      const utilizationRate = (current / capacity) * 100;
      
      let status: SiloStatus['status'] = 'normal';
      if (current <= 0) status = 'empty';
      else if (utilizationRate > 90 || available < reserved) status = 'critical';
      else if (utilizationRate < 20 || utilizationRate > 80) status = 'warning';

      return {
        id: silo.id,
        capacity: Math.round(capacity),
        current: Math.round(current * 10) / 10,
        reserved: Math.round(reserved * 10) / 10,
        available: Math.round(available * 10) / 10,
        utilizationRate: Math.round(utilizationRate * 10) / 10,
        status,
        lastUpdated: new Date().toLocaleTimeString()
      } as SiloStatus;
    });
  }, [silos, isRefreshing]);

  const filteredSilos = silos.filter(silo => {
    const matchesStation = silo.stationId === currentStation;
    const matchesSearch = searchTerm === '' || 
      silo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      silo.identifier.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || silo.materialType === filterType;
    
    if (filterStatus !== 'all') {
      const status = siloStatuses.find(s => s.id === silo.id)?.status;
      const matchesStatus = status === filterStatus;
      return matchesStation && matchesSearch && matchesType && matchesStatus;
    }
    
    return matchesStation && matchesSearch && matchesType;
  });

  function getSiloCapacityByType(materialType: string): number {
    switch (materialType) {
      case 'cement': return 200;
      case 'water': return 50;
      case 'sand':
      case 'stone': return 300;
      case 'additive': return 20;
      default: return 100;
    }
  }

  const handleEdit = (silo: SiloInfo) => {
    setEditingId(silo.id);
    setEditingSilo(silo);
  };

  const handleSave = () => {
    if (!editingId || !editingSilo) return;

    const updatedSilos = silos.map(s => 
      s.id === editingId ? { ...s, ...editingSilo } : s
    );
    onSilosChange(updatedSilos);
    setEditingId(null);
    setEditingSilo({});
    
    toast({
      title: '保存成功',
      description: '料仓信息已更新'
    });
  };

  const handleCancel = () => {
    setEditingId(null);
    setEditingSilo({});
  };

  const handleAdd = () => {
    const newSilo: SiloInfo = {
      id: `silo-${Date.now()}`,
      identifier: `新料仓${filteredSilos.length + 1}`,
      name: `${filteredSilos.length + 1}#料仓`,
      displayOrder: filteredSilos.length + 1,
      isEnabled: true,
      stationId: currentStation,
      materialType: 'cement'
    };

    onSilosChange([...silos, newSilo]);
    toast({
      title: '添加成功',
      description: '新料仓已添加'
    });
  };

  const handleDelete = (id: string) => {
    const updatedSilos = silos.filter(s => s.id !== id);
    onSilosChange(updatedSilos);
    
    toast({
      title: '删除成功',
      description: '料仓已删除'
    });
  };

  const handleToggleEnabled = (id: string, enabled: boolean) => {
    const updatedSilos = silos.map(s => 
      s.id === id ? { ...s, isEnabled: enabled } : s
    );
    onSilosChange(updatedSilos);
    
    toast({
      title: enabled ? '料仓已启用' : '料仓已禁用',
      description: `料仓 ${silos.find(s => s.id === id)?.name} ${enabled ? '启用' : '禁用'}成功`
    });
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
    toast({
      title: "刷新成功",
      description: "料仓状态已更新"
    });
  };

  const handleExport = () => {
    const data = filteredSilos.map(silo => {
      const status = siloStatuses.find(s => s.id === silo.id);
      return {
        料仓名称: silo.name,
        标识符: silo.identifier,
        材料类型: materialTypes.find(t => t.value === silo.materialType)?.label,
        容量: status?.capacity + 't',
        当前库存: status?.current + 't',
        利用率: status?.utilizationRate + '%',
        状态: status?.status === 'normal' ? '正常' : 
              status?.status === 'warning' ? '警告' : 
              status?.status === 'critical' ? '严重' : '空仓',
        是否启用: silo.isEnabled ? '是' : '否'
      };
    });
    
    console.log('导出数据:', data);
    toast({
      title: "导出成功",
      description: `已导出 ${data.length} 条料仓数据`
    });
  };

  const getStatusBadge = (status: SiloStatus['status']) => {
    switch (status) {
      case 'normal':
        return <Badge className="bg-green-100 text-green-800">正常</Badge>;
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800">警告</Badge>;
      case 'critical':
        return <Badge className="bg-red-100 text-red-800">严重</Badge>;
      case 'empty':
        return <Badge variant="secondary">空仓</Badge>;
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  const getStatusIcon = (status: SiloStatus['status']) => {
    switch (status) {
      case 'normal': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'critical': return <XCircle className="h-4 w-4 text-red-600" />;
      case 'empty': return <TrendingDown className="h-4 w-4 text-gray-400" />;
      default: return <Warehouse className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Warehouse className="h-5 w-5" />
              料仓管理
            </span>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                刷新
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
              >
                <Download className="h-4 w-4 mr-1" />
                导出
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="management">料仓管理</TabsTrigger>
            <TabsTrigger value="status">状态监控</TabsTrigger>
            <TabsTrigger value="statistics">统计分析</TabsTrigger>
          </TabsList>

          <TabsContent value="management" className="space-y-4 mt-4">
            {/* 筛选和搜索 */}
            <Card>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">搅拌站</Label>
                    <Select value={currentStation} onValueChange={setCurrentStation}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择搅拌站" />
                      </SelectTrigger>
                      <SelectContent>
                        {stations.map((station) => (
                          <SelectItem key={station.id} value={station.id}>
                            {station.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">搜索</Label>
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="搜索料仓名称..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">材料类型</Label>
                    <Select value={filterType} onValueChange={(value) => setFilterType(value as any)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部类型</SelectItem>
                        {materialTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">状态筛选</Label>
                    <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value as any)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部状态</SelectItem>
                        <SelectItem value="normal">正常</SelectItem>
                        <SelectItem value="warning">警告</SelectItem>
                        <SelectItem value="critical">严重</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex justify-between items-center mt-4">
                  <div className="flex items-center space-x-4">
                    <div className="text-sm text-muted-foreground">
                      共 {filteredSilos.length} 个料仓
                    </div>
                    <Badge variant="outline" className="bg-green-50">
                      启用: {filteredSilos.filter(s => s.isEnabled).length}
                    </Badge>
                    <Badge variant="outline" className="bg-red-50">
                      禁用: {filteredSilos.filter(s => !s.isEnabled).length}
                    </Badge>
                  </div>
                  <Button onClick={handleAdd}>
                    <Plus className="mr-2 h-4 w-4" />
                    添加料仓
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 料仓列表 */}
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>料仓名称</TableHead>
                      <TableHead>标识符</TableHead>
                      <TableHead>材料类型</TableHead>
                      <TableHead>显示顺序</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>库存状态</TableHead>
                      <TableHead>启用状态</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSilos.map((silo) => {
                      const status = siloStatuses.find(s => s.id === silo.id);
                      const isEditing = editingId === silo.id;

                      return (
                        <TableRow key={silo.id}>
                          <TableCell>
                            {isEditing ? (
                              <Input
                                value={editingSilo.name || ''}
                                onChange={(e) => setEditingSilo({...editingSilo, name: e.target.value})}
                                className="h-8"
                              />
                            ) : (
                              silo.name
                            )}
                          </TableCell>

                          <TableCell>
                            {isEditing ? (
                              <Input
                                value={editingSilo.identifier || ''}
                                onChange={(e) => setEditingSilo({...editingSilo, identifier: e.target.value})}
                                className="h-8"
                              />
                            ) : (
                              silo.identifier
                            )}
                          </TableCell>

                          <TableCell>
                            {isEditing ? (
                              <Select
                                value={editingSilo.materialType || silo.materialType}
                                onValueChange={(value) => setEditingSilo({...editingSilo, materialType: value as MaterialType})}
                              >
                                <SelectTrigger className="h-8">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {materialTypes.map((type) => (
                                    <SelectItem key={type.value} value={type.value}>
                                      {type.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            ) : (
                              materialTypes.find(t => t.value === silo.materialType)?.label
                            )}
                          </TableCell>

                          <TableCell>
                            {isEditing ? (
                              <Input
                                type="number"
                                value={editingSilo.displayOrder || ''}
                                onChange={(e) => setEditingSilo({...editingSilo, displayOrder: Number(e.target.value)})}
                                className="h-8"
                              />
                            ) : (
                              silo.displayOrder
                            )}
                          </TableCell>

                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {status && getStatusIcon(status.status)}
                              {status && getStatusBadge(status.status)}
                            </div>
                          </TableCell>

                          <TableCell>
                            {status && (
                              <div className="space-y-1">
                                <div className="text-xs text-muted-foreground">
                                  {status.current}t / {status.capacity}t
                                </div>
                                <Progress value={status.utilizationRate} className="h-2" />
                                <div className="text-xs text-muted-foreground">
                                  {status.utilizationRate}%
                                </div>
                              </div>
                            )}
                          </TableCell>

                          <TableCell>
                            <Checkbox
                              checked={silo.isEnabled}
                              onCheckedChange={(checked) => handleToggleEnabled(silo.id, checked as boolean)}
                            />
                          </TableCell>

                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {isEditing ? (
                                <>
                                  <Button size="sm" onClick={handleSave}>
                                    <Save className="h-4 w-4" />
                                  </Button>
                                  <Button size="sm" variant="outline" onClick={handleCancel}>
                                    <X className="h-4 w-4" />
                                  </Button>
                                </>
                              ) : (
                                <>
                                  <Button size="sm" variant="outline" onClick={() => handleEdit(silo)}>
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button size="sm" variant="destructive" onClick={() => handleDelete(silo.id)}>
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="status" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredSilos.map((silo) => {
                const status = siloStatuses.find(s => s.id === silo.id);
                if (!status) return null;

                return (
                  <Card key={silo.id}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center justify-between">
                        <span>{silo.name}</span>
                        {getStatusBadge(status.status)}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>库存量</span>
                          <span className="font-medium">{status.current}t</span>
                        </div>
                        <Progress value={status.utilizationRate} className="h-2" />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>0t</span>
                          <span>{status.utilizationRate}%</span>
                          <span>{status.capacity}t</span>
                        </div>
                      </div>

                      <Separator />

                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className="text-muted-foreground">可用:</span>
                          <div className="font-medium">{status.available}t</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">预留:</span>
                          <div className="font-medium">{status.reserved}t</div>
                        </div>
                      </div>

                      <div className="text-xs text-muted-foreground">
                        更新时间: {status.lastUpdated}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="statistics" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">总料仓数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{filteredSilos.length}</div>
                  <div className="text-xs text-muted-foreground">
                    启用 {filteredSilos.filter(s => s.isEnabled).length} 个
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">平均利用率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Math.round(siloStatuses.reduce((sum, s) => sum + s.utilizationRate, 0) / siloStatuses.length)}%
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <TrendingUp className="inline h-3 w-3 mr-1" />
                    较上周 +5%
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">警告料仓</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-yellow-600">
                    {siloStatuses.filter(s => s.status === 'warning').length}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    需要关注
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">严重料仓</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">
                    {siloStatuses.filter(s => s.status === 'critical').length}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    需要立即处理
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">材料类型分布</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {materialTypes.map((type) => {
                    const count = filteredSilos.filter(s => s.materialType === type.value).length;
                    const percentage = filteredSilos.length > 0 ? (count / filteredSilos.length) * 100 : 0;

                    return (
                      <div key={type.value} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span>{type.label}</span>
                          <span>{count} 个 ({Math.round(percentage)}%)</span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
