'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  History, 
  Search, 
  Filter, 
  Download, 
  <PERSON>, 
  <PERSON><PERSON>, 
  Calendar as CalendarIcon,
  RotateCcw,
  FileText,
  TrendingUp
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface RatioHistoryRecord {
  id: string;
  taskNumber: string;
  projectName: string;
  strength: string;
  ratioCode: string;
  stationId: string;
  stationName: string;
  operator: string;
  createdAt: string;
  status: 'draft' | 'approved' | 'sent' | 'completed';
  materials: Array<{
    name: string;
    type: string;
    amount: number;
    actualAmount: number;
  }>;
  parameters: {
    density: number;
    waterRatio: number;
    waterAmount: number;
    sandRatio: number;
    totalWeight: number;
  };
  notes?: string;
  version: number;
}

interface RatioHistoryProps {
  onClose: () => void;
  onApplyRatio?: (record: RatioHistoryRecord) => void;
}

export function RatioHistoryV2({ onClose, onApplyRatio }: RatioHistoryProps) {
  const [records, setRecords] = useState<RatioHistoryRecord[]>([
    {
      id: '1',
      taskNumber: 'T001',
      projectName: '测试工程项目',
      strength: 'C30',
      ratioCode: 'C30-001',
      stationId: 'station1',
      stationName: '搅拌站A',
      operator: '张三',
      createdAt: '2024-01-15 14:30:00',
      status: 'completed',
      materials: [
        { name: '水泥', type: 'powder', amount: 400, actualAmount: 400 },
        { name: '水', type: 'water', amount: 180, actualAmount: 180 },
        { name: '中砂', type: 'aggregate', amount: 650, actualAmount: 627 },
        { name: '石子', type: 'aggregate', amount: 1100, actualAmount: 1089 },
        { name: '减水剂', type: 'additive', amount: 5.2, actualAmount: 5.2 }
      ],
      parameters: {
        density: 2.36,
        waterRatio: 0.45,
        waterAmount: 180,
        sandRatio: 35,
        totalWeight: 2360
      },
      notes: '标准C30配比，质量良好',
      version: 1
    },
    {
      id: '2',
      taskNumber: 'T002',
      projectName: '商业综合体',
      strength: 'C25',
      ratioCode: 'C25-001',
      stationId: 'station1',
      stationName: '搅拌站A',
      operator: '李四',
      createdAt: '2024-01-14 16:20:00',
      status: 'completed',
      materials: [
        { name: '水泥', type: 'powder', amount: 300, actualAmount: 300 },
        { name: '粉煤灰', type: 'powder', amount: 50, actualAmount: 50 },
        { name: '水', type: 'water', amount: 175, actualAmount: 175 },
        { name: '中砂', type: 'aggregate', amount: 680, actualAmount: 660 },
        { name: '石子', type: 'aggregate', amount: 1110, actualAmount: 1099 },
        { name: '减水剂', type: 'additive', amount: 3.5, actualAmount: 3.5 }
      ],
      parameters: {
        density: 2.35,
        waterRatio: 0.50,
        waterAmount: 175,
        sandRatio: 38,
        totalWeight: 2350
      },
      notes: '经济型配比，含15%粉煤灰',
      version: 2
    },
    {
      id: '3',
      taskNumber: 'T003',
      projectName: '住宅小区',
      strength: 'C30',
      ratioCode: 'C30-002',
      stationId: 'station2',
      stationName: '搅拌站B',
      operator: '王五',
      createdAt: '2024-01-13 10:15:00',
      status: 'approved',
      materials: [
        { name: '水泥', type: 'powder', amount: 420, actualAmount: 420 },
        { name: '水', type: 'water', amount: 185, actualAmount: 185 },
        { name: '中砂', type: 'aggregate', amount: 630, actualAmount: 608 },
        { name: '石子', type: 'aggregate', amount: 1080, actualAmount: 1069 },
        { name: '减水剂', type: 'additive', amount: 6.3, actualAmount: 6.3 }
      ],
      parameters: {
        density: 2.37,
        waterRatio: 0.44,
        waterAmount: 185,
        sandRatio: 34,
        totalWeight: 2370
      },
      notes: '高强度配比，适用于承重结构',
      version: 1
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterStrength, setFilterStrength] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({});
  const [selectedRecord, setSelectedRecord] = useState<RatioHistoryRecord | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const { toast } = useToast();

  const statusOptions = [
    { value: 'all', label: '全部状态' },
    { value: 'draft', label: '草稿' },
    { value: 'approved', label: '已审核' },
    { value: 'sent', label: '已发送' },
    { value: 'completed', label: '已完成' }
  ];

  const strengthOptions = [
    { value: 'all', label: '全部强度' },
    { value: 'C20', label: 'C20' },
    { value: 'C25', label: 'C25' },
    { value: 'C30', label: 'C30' },
    { value: 'C35', label: 'C35' },
    { value: 'C40', label: 'C40' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'sent': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft': return '草稿';
      case 'approved': return '已审核';
      case 'sent': return '已发送';
      case 'completed': return '已完成';
      default: return '未知';
    }
  };

  const filteredRecords = records.filter(record => {
    const matchesSearch = record.taskNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.operator.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || record.status === filterStatus;
    const matchesStrength = filterStrength === 'all' || record.strength === filterStrength;
    
    let matchesDate = true;
    if (dateRange.from && dateRange.to) {
      const recordDate = new Date(record.createdAt);
      matchesDate = recordDate >= dateRange.from && recordDate <= dateRange.to;
    }
    
    return matchesSearch && matchesStatus && matchesStrength && matchesDate;
  });

  const handleViewDetail = (record: RatioHistoryRecord) => {
    setSelectedRecord(record);
    setIsDetailModalOpen(true);
  };

  const handleApply = (record: RatioHistoryRecord) => {
    onApplyRatio?.(record);
    toast({
      title: '应用成功',
      description: `已应用配比记录: ${record.ratioCode}`
    });
    onClose();
  };

  const handleCopy = (record: RatioHistoryRecord) => {
    const ratioData = {
      parameters: record.parameters,
      materials: record.materials
    };
    navigator.clipboard.writeText(JSON.stringify(ratioData, null, 2));
    toast({
      title: '复制成功',
      description: '配比数据已复制到剪贴板'
    });
  };

  const handleExport = () => {
    const dataStr = JSON.stringify(filteredRecords, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `ratio_history_${format(new Date(), 'yyyy-MM-dd')}.json`;
    link.click();
    URL.revokeObjectURL(url);
    
    toast({
      title: '导出成功',
      description: '历史记录已导出'
    });
  };

  return (
    <div className="space-y-4">
      <Tabs defaultValue="list" className="space-y-4">
        <TabsList>
          <TabsTrigger value="list">历史记录</TabsTrigger>
          <TabsTrigger value="stats">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          {/* 搜索和过滤 */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between space-x-4">
                <div className="flex items-center space-x-4 flex-1">
                  <div className="relative flex-1 max-w-sm">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索任务编号、工程名称或操作员..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                  
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {statusOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <Select value={filterStrength} onValueChange={setFilterStrength}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {strengthOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-48">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateRange.from ? (
                          dateRange.to ? (
                            <>
                              {format(dateRange.from, "yyyy-MM-dd")} -{" "}
                              {format(dateRange.to, "yyyy-MM-dd")}
                            </>
                          ) : (
                            format(dateRange.from, "yyyy-MM-dd")
                          )
                        ) : (
                          "选择日期范围"
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        initialFocus
                        mode="range"
                        defaultMonth={dateRange.from}
                        selected={dateRange}
                        onSelect={setDateRange}
                        numberOfMonths={2}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                
                <Button size="sm" onClick={handleExport}>
                  <Download className="mr-2 h-4 w-4" />
                  导出
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 历史记录表格 */}
          <Card>
            <CardContent className="p-0">
              <div className="max-h-96 overflow-auto">
                <Table>
                  <TableHeader className="sticky top-0 bg-background">
                    <TableRow>
                      <TableHead>任务编号</TableHead>
                      <TableHead>工程名称</TableHead>
                      <TableHead>强度</TableHead>
                      <TableHead>配比编号</TableHead>
                      <TableHead>搅拌站</TableHead>
                      <TableHead>操作员</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead>版本</TableHead>
                      <TableHead className="w-40">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell className="font-medium">{record.taskNumber}</TableCell>
                        <TableCell>{record.projectName}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{record.strength}</Badge>
                        </TableCell>
                        <TableCell>
                          <code className="text-xs bg-muted px-1 py-0.5 rounded">
                            {record.ratioCode}
                          </code>
                        </TableCell>
                        <TableCell>{record.stationName}</TableCell>
                        <TableCell>{record.operator}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(record.status)}>
                            {getStatusLabel(record.status)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-xs text-muted-foreground">
                          {format(new Date(record.createdAt), 'yyyy-MM-dd HH:mm')}
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">v{record.version}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            <Button size="sm" variant="outline" onClick={() => handleViewDetail(record)}>
                              <Eye className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleCopy(record)}>
                              <Copy className="h-3 w-3" />
                            </Button>
                            <Button size="sm" onClick={() => handleApply(record)}>
                              <RotateCcw className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats" className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{records.length}</div>
                  <div className="text-sm text-muted-foreground">总记录数</div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {records.filter(r => r.status === 'completed').length}
                  </div>
                  <div className="text-sm text-muted-foreground">已完成</div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {new Set(records.map(r => r.strength)).size}
                  </div>
                  <div className="text-sm text-muted-foreground">强度等级</div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {new Set(records.map(r => r.operator)).size}
                  </div>
                  <div className="text-sm text-muted-foreground">操作员</div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                强度等级分布
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {strengthOptions.slice(1).map((strength) => {
                  const count = records.filter(r => r.strength === strength.value).length;
                  const percentage = records.length > 0 ? (count / records.length) * 100 : 0;
                  return (
                    <div key={strength.value} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{strength.label}</Badge>
                        <span className="text-sm">{count} 条记录</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {percentage.toFixed(1)}%
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 详情模态框 */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              配比详情 - {selectedRecord?.ratioCode}
            </DialogTitle>
          </DialogHeader>
          
          {selectedRecord && (
            <div className="space-y-4">
              {/* 基本信息 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">基本信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <Label>任务编号</Label>
                      <div className="font-medium">{selectedRecord.taskNumber}</div>
                    </div>
                    <div>
                      <Label>工程名称</Label>
                      <div className="font-medium">{selectedRecord.projectName}</div>
                    </div>
                    <div>
                      <Label>强度等级</Label>
                      <div className="font-medium">{selectedRecord.strength}</div>
                    </div>
                    <div>
                      <Label>搅拌站</Label>
                      <div className="font-medium">{selectedRecord.stationName}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 计算参数 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">计算参数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-5 gap-4 text-sm">
                    <div>
                      <Label>密度</Label>
                      <div className="font-medium">{selectedRecord.parameters.density} t/m³</div>
                    </div>
                    <div>
                      <Label>水胶比</Label>
                      <div className="font-medium">{selectedRecord.parameters.waterRatio}</div>
                    </div>
                    <div>
                      <Label>用水量</Label>
                      <div className="font-medium">{selectedRecord.parameters.waterAmount} kg</div>
                    </div>
                    <div>
                      <Label>砂率</Label>
                      <div className="font-medium">{selectedRecord.parameters.sandRatio}%</div>
                    </div>
                    <div>
                      <Label>总重量</Label>
                      <div className="font-medium">{selectedRecord.parameters.totalWeight} kg</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 材料配比 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">材料配比</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>材料名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>理论用量</TableHead>
                        <TableHead>实际用量</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedRecord.materials.map((material, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{material.name}</TableCell>
                          <TableCell>{material.type}</TableCell>
                          <TableCell>{material.amount} kg</TableCell>
                          <TableCell>{material.actualAmount} kg</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>

              {/* 备注 */}
              {selectedRecord.notes && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">备注</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm">{selectedRecord.notes}</div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
