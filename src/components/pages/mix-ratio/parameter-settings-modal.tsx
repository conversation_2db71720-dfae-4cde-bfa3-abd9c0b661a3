'use client';

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Settings, Save, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ParameterSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ParameterSettingsModal({ isOpen, onClose }: ParameterSettingsModalProps) {
  const [settings, setSettings] = useState({
    // 物料分配设置
    materialDistribution: 'average', // 'average' | 'capacity'
    
    // 计算精度设置
    calculationPrecision: {
      weight: 1, // 重量精度（小数位）
      ratio: 2,  // 比例精度（小数位）
      density: 3 // 密度精度（小数位）
    },
    
    // 自动保存设置
    autoSave: true,
    autoSaveInterval: 5, // 分钟
    
    // 配比验证设置
    validation: {
      enableRangeCheck: true,
      enableRatioCheck: true,
      enableMaterialCheck: true
    },
    
    // 界面显示设置
    display: {
      showMaterialCode: true,
      showDensity: true,
      showSequence: true,
      compactMode: false
    },
    
    // 打印设置
    print: {
      includeHeader: true,
      includeFooter: true,
      includeQRCode: true,
      paperSize: 'A4'
    }
  });

  const { toast } = useToast();

  const handleSave = () => {
    // 保存参数设置
    localStorage.setItem('mixRatioSettings', JSON.stringify(settings));
    
    toast({
      title: '保存成功',
      description: '参数设置已保存'
    });
    
    onClose();
  };

  const handleReset = () => {
    // 重置为默认设置
    setSettings({
      materialDistribution: 'average',
      calculationPrecision: {
        weight: 1,
        ratio: 2,
        density: 3
      },
      autoSave: true,
      autoSaveInterval: 5,
      validation: {
        enableRangeCheck: true,
        enableRatioCheck: true,
        enableMaterialCheck: true
      },
      display: {
        showMaterialCode: true,
        showDensity: true,
        showSequence: true,
        compactMode: false
      },
      print: {
        includeHeader: true,
        includeFooter: true,
        includeQRCode: true,
        paperSize: 'A4'
      }
    });
    
    toast({
      title: '重置成功',
      description: '参数已重置为默认值'
    });
  };

  const updateSetting = (path: string, value: any) => {
    const keys = path.split('.');
    const newSettings = { ...settings };
    let current: any = newSettings;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    setSettings(newSettings);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            参数设定
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Tabs defaultValue="distribution" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="distribution">物料分配</TabsTrigger>
              <TabsTrigger value="calculation">计算设置</TabsTrigger>
              <TabsTrigger value="display">界面设置</TabsTrigger>
              <TabsTrigger value="print">打印设置</TabsTrigger>
            </TabsList>

            <TabsContent value="distribution" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">物料分配方式</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">
                      当有两个料仓的配置信息一致时，物料分配方式：
                    </Label>
                    
                    <RadioGroup
                      value={settings.materialDistribution}
                      onValueChange={(value) => updateSetting('materialDistribution', value)}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="average" id="average" />
                        <Label htmlFor="average" className="text-sm">
                          A) 平均分配物料
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="capacity" id="capacity" />
                        <Label htmlFor="capacity" className="text-sm">
                          B) 根据料仓上限来分配物料
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">分配算法说明：</Label>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <div>• <strong>平均分配</strong>：将所需物料平均分配到可用的料仓中</div>
                      <div>• <strong>按容量分配</strong>：根据各料仓的最大容量比例来分配物料</div>
                      <div>• 分配时会优先考虑料仓的启用状态和材料类型匹配</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="calculation" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">计算精度设置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm">重量精度（小数位）</Label>
                      <Select
                        value={settings.calculationPrecision.weight.toString()}
                        onValueChange={(value) => updateSetting('calculationPrecision.weight', Number(value))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">0位（整数）</SelectItem>
                          <SelectItem value="1">1位</SelectItem>
                          <SelectItem value="2">2位</SelectItem>
                          <SelectItem value="3">3位</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-sm">比例精度（小数位）</Label>
                      <Select
                        value={settings.calculationPrecision.ratio.toString()}
                        onValueChange={(value) => updateSetting('calculationPrecision.ratio', Number(value))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1位</SelectItem>
                          <SelectItem value="2">2位</SelectItem>
                          <SelectItem value="3">3位</SelectItem>
                          <SelectItem value="4">4位</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-sm">密度精度（小数位）</Label>
                      <Select
                        value={settings.calculationPrecision.density.toString()}
                        onValueChange={(value) => updateSetting('calculationPrecision.density', Number(value))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="2">2位</SelectItem>
                          <SelectItem value="3">3位</SelectItem>
                          <SelectItem value="4">4位</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">自动保存设置</Label>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          checked={settings.autoSave}
                          onCheckedChange={(checked) => updateSetting('autoSave', checked)}
                        />
                        <Label className="text-sm">启用自动保存</Label>
                      </div>
                      
                      {settings.autoSave && (
                        <div className="flex items-center space-x-2">
                          <Label className="text-sm">间隔时间:</Label>
                          <Input
                            type="number"
                            value={settings.autoSaveInterval}
                            onChange={(e) => updateSetting('autoSaveInterval', Number(e.target.value))}
                            className="w-16 h-8"
                            min="1"
                            max="60"
                          />
                          <span className="text-sm text-muted-foreground">分钟</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">配比验证设置</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          checked={settings.validation.enableRangeCheck}
                          onCheckedChange={(checked) => updateSetting('validation.enableRangeCheck', checked)}
                        />
                        <Label className="text-sm">启用数值范围检查</Label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          checked={settings.validation.enableRatioCheck}
                          onCheckedChange={(checked) => updateSetting('validation.enableRatioCheck', checked)}
                        />
                        <Label className="text-sm">启用配比合理性检查</Label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          checked={settings.validation.enableMaterialCheck}
                          onCheckedChange={(checked) => updateSetting('validation.enableMaterialCheck', checked)}
                        />
                        <Label className="text-sm">启用材料兼容性检查</Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="display" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">界面显示设置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">表格显示选项</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          checked={settings.display.showMaterialCode}
                          onCheckedChange={(checked) => updateSetting('display.showMaterialCode', checked)}
                        />
                        <Label className="text-sm">显示材料代码</Label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          checked={settings.display.showDensity}
                          onCheckedChange={(checked) => updateSetting('display.showDensity', checked)}
                        />
                        <Label className="text-sm">显示材料密度</Label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          checked={settings.display.showSequence}
                          onCheckedChange={(checked) => updateSetting('display.showSequence', checked)}
                        />
                        <Label className="text-sm">显示序号</Label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          checked={settings.display.compactMode}
                          onCheckedChange={(checked) => updateSetting('display.compactMode', checked)}
                        />
                        <Label className="text-sm">紧凑模式</Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="print" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">打印设置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">打印内容</Label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={settings.print.includeHeader}
                            onCheckedChange={(checked) => updateSetting('print.includeHeader', checked)}
                          />
                          <Label className="text-sm">包含页眉</Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={settings.print.includeFooter}
                            onCheckedChange={(checked) => updateSetting('print.includeFooter', checked)}
                          />
                          <Label className="text-sm">包含页脚</Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={settings.print.includeQRCode}
                            onCheckedChange={(checked) => updateSetting('print.includeQRCode', checked)}
                          />
                          <Label className="text-sm">包含二维码</Label>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">纸张设置</Label>
                      <Select
                        value={settings.print.paperSize}
                        onValueChange={(value) => updateSetting('print.paperSize', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="A4">A4</SelectItem>
                          <SelectItem value="A3">A3</SelectItem>
                          <SelectItem value="Letter">Letter</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* 底部操作按钮 */}
        <div className="flex justify-between pt-4 border-t">
          <Button variant="outline" onClick={handleReset}>
            <RefreshCw className="mr-2 h-4 w-4" />
            重置为默认
          </Button>
          
          <div className="flex space-x-2">
            <Button onClick={handleSave}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
            <Button variant="outline" onClick={onClose}>
              关闭
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
