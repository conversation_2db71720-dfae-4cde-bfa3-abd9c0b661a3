'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Settings,
  Database,
  Warehouse,
  History,
  FileText,
  Target,
  Building2,
  Zap,
  BookOpen
} from 'lucide-react';

interface OperationToolbarProps {
  selectedStation: string;
  stations: Array<{ id: string; name: string; code: string; isActive: boolean }>;
  onStationChange: (stationId: string) => void;
  onMaterialManagement: () => void;
  onSiloManagement: () => void;
  onRatioHistory: () => void;
  onParameterSettings: () => void;
  onQualityStandards: () => void;
  onUnifiedRatio: () => void;
  onTemplateManagement: () => void;
}

export function OperationToolbar({
  selectedStation,
  stations,
  onStationChange,
  onMaterialManagement,
  onSiloManagement,
  onRatioHistory,
  onParameterSettings,
  onQualityStandards,
  onUnifiedRatio,
  onTemplateManagement
}: OperationToolbarProps) {
  const currentStation = stations.find(s => s.id === selectedStation);

  return (
    <Card className="border-b rounded-none border-l-0 border-r-0 border-t-0">
      <CardContent className="p-3">
        <div className="flex items-center justify-between">
          {/* 左侧：搅拌站选择和状态 */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">搅拌站:</span>
              <Select value={selectedStation} onValueChange={onStationChange}>
                <SelectTrigger className="w-48 h-8">
                  <SelectValue placeholder="选择搅拌站" />
                </SelectTrigger>
                <SelectContent>
                  {stations.map((station) => (
                    <SelectItem key={station.id} value={station.id}>
                      <div className="flex items-center space-x-2">
                        <span>{station.name}</span>
                        <Badge 
                          variant={station.isActive ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {station.code}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {currentStation && (
              <Badge 
                variant={currentStation.isActive ? "default" : "destructive"}
                className="text-xs"
              >
                {currentStation.isActive ? "运行中" : "停用"}
              </Badge>
            )}
          </div>

          {/* 右侧：操作按钮组 */}
          <div className="flex items-center space-x-2">
            {/* 统一配比 */}
            <Button
              variant="outline"
              size="sm"
              onClick={onUnifiedRatio}
              className="h-8 text-xs"
            >
              <Zap className="mr-1 h-3 w-3" />
              所有站统一配比
            </Button>

            <Separator orientation="vertical" className="h-6" />

            {/* 管理功能 */}
            <Button
              variant="outline"
              size="sm"
              onClick={onMaterialManagement}
              className="h-8 text-xs"
            >
              <Database className="mr-1 h-3 w-3" />
              材料管理
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={onSiloManagement}
              className="h-8 text-xs"
            >
              <Warehouse className="mr-1 h-3 w-3" />
              料仓管理
            </Button>

            <Separator orientation="vertical" className="h-6" />

            {/* 历史和设置 */}
            <Button
              variant="outline"
              size="sm"
              onClick={onTemplateManagement}
              className="h-8 text-xs"
            >
              <BookOpen className="mr-1 h-3 w-3" />
              配比模板
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={onRatioHistory}
              className="h-8 text-xs"
            >
              <History className="mr-1 h-3 w-3" />
              配比历史
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={onParameterSettings}
              className="h-8 text-xs"
            >
              <Settings className="mr-1 h-3 w-3" />
              参数设定
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={onQualityStandards}
              className="h-8 text-xs"
            >
              <Target className="mr-1 h-3 w-3" />
              质量标准
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
