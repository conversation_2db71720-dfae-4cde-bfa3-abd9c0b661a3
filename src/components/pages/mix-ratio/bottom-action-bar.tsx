'use client';

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Save, 
  Send, 
  X, 
  User, 
  Calendar,
  AlertCircle,
  CheckCircle2,
  Clock
} from 'lucide-react';

interface BottomActionBarProps {
  productionTips: string;
  serialNumber: string;
  createdBy: string;
  createdAt: string;
  lastModifiedBy: string;
  lastModifiedAt: string;
  isSync: boolean;
  status: 'draft' | 'approved' | 'sent';
  onProductionTipsChange: (tips: string) => void;
  onSerialNumberChange: (serialNumber: string) => void;
  onSyncChange: (isSync: boolean) => void;
  onSave: () => void;
  onSubmit: () => void;
  onExit: () => void;
  isSaving?: boolean;
  isSubmitting?: boolean;
}

export function BottomActionBar({
  productionTips,
  serialNumber,
  createdBy,
  createdAt,
  lastModifiedBy,
  lastModifiedAt,
  isSync,
  status,
  onProductionTipsChange,
  onSerialNumberChange,
  onSyncChange,
  onSave,
  onSubmit,
  onExit,
  isSaving = false,
  isSubmitting = false
}: BottomActionBarProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'draft':
        return { 
          label: '草稿', 
          variant: 'secondary' as const, 
          icon: <Clock className="h-3 w-3" /> 
        };
      case 'approved':
        return { 
          label: '已审核', 
          variant: 'default' as const, 
          icon: <CheckCircle2 className="h-3 w-3" /> 
        };
      case 'sent':
        return { 
          label: '已发送', 
          variant: 'destructive' as const, 
          icon: <Send className="h-3 w-3" /> 
        };
      default:
        return { 
          label: '未知', 
          variant: 'outline' as const, 
          icon: <AlertCircle className="h-3 w-3" /> 
        };
    }
  };

  const statusInfo = getStatusInfo(status);

  return (
    <Card className="border-t rounded-none border-l-0 border-r-0 border-b-0">
      <CardContent className="p-4 space-y-4">
        {/* 第一行：生产提示和流水号 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <Label className="text-sm font-medium">搅拌生产时提示内容</Label>
            <Input
              value={productionTips}
              onChange={(e) => onProductionTipsChange(e.target.value)}
              placeholder="请输入生产提示内容..."
              className="h-8 text-sm"
            />
          </div>
          
          <div className="space-y-1">
            <Label className="text-sm font-medium">流水号</Label>
            <Input
              value={serialNumber}
              onChange={(e) => onSerialNumberChange(e.target.value)}
              placeholder="请输入流水号..."
              className="h-8 text-sm"
            />
          </div>
        </div>

        <Separator />

        {/* 第二行：创建和修改信息 */}
        <div className="grid grid-cols-2 gap-6">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">创建:</span>
              <span className="text-sm font-medium">{formatDate(createdAt)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{createdBy}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">修改:</span>
              <span className="text-sm font-medium">{formatDate(lastModifiedAt)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{lastModifiedBy}</span>
            </div>
          </div>
        </div>

        <Separator />

        {/* 第三行：状态和操作 */}
        <div className="flex items-center justify-between">
          {/* 左侧：状态和同步选项 */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">状态:</span>
              <Badge variant={statusInfo.variant} className="text-xs">
                {statusInfo.icon}
                <span className="ml-1">{statusInfo.label}</span>
              </Badge>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="sync"
                checked={isSync}
                onCheckedChange={onSyncChange}
              />
              <Label htmlFor="sync" className="text-sm cursor-pointer">
                同步（同选用配比）
              </Label>
            </div>
          </div>

          {/* 右侧：操作按钮 */}
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={onSave}
              disabled={isSaving || isSubmitting}
              className="h-8"
            >
              {isSaving ? (
                <>
                  <Clock className="mr-2 h-4 w-4 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  保存
                </>
              )}
            </Button>

            <Button
              onClick={onSubmit}
              disabled={isSaving || isSubmitting || status === 'sent'}
              className="h-8"
            >
              {isSubmitting ? (
                <>
                  <Clock className="mr-2 h-4 w-4 animate-spin" />
                  发送中...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  审核-发送
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={onExit}
              disabled={isSaving || isSubmitting}
              className="h-8"
            >
              <X className="mr-2 h-4 w-4" />
              退出
            </Button>
          </div>
        </div>

        {/* 操作提示 */}
        {status === 'sent' && (
          <div className="flex items-center space-x-2 p-2 bg-muted rounded-md">
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              配比已发送，无法再次提交。如需修改，请联系管理员。
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
