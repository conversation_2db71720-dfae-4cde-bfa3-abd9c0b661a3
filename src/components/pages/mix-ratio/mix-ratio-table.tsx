'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { CheckSquare, Plus, Minus, Save, FileText } from 'lucide-react';

import type { MixRatio, Material, SiloInfo, MixRatioItem } from '@/types/mixRatio';

interface MixRatioTableProps {
  mixRatio: MixRatio;
  onUpdate: (mixRatio: MixRatio) => void;
  materials: Material[];
  silos: SiloInfo[];
  onOpenQualityStandard: () => void;
}

export function MixRatioTable({ 
  mixRatio, 
  onUpdate, 
  materials, 
  silos,
  onOpenQualityStandard 
}: MixRatioTableProps) {
  const [storageDisplayMode, setStorageDisplayMode] = useState<'silo' | 'station' | 'code'>('silo');

  const handleItemChange = (itemId: string, field: keyof MixRatioItem, value: any) => {
    const updatedItems = mixRatio.items.map(item => 
      item.id === itemId ? { ...item, [field]: value } : item
    );
    
    onUpdate({
      ...mixRatio,
      items: updatedItems
    });
  };

  const handleAddItem = () => {
    const newItem: MixRatioItem = {
      id: Date.now().toString(),
      materialId: '',
      materialName: '',
      specification: '',
      theoreticalAmount: 0,
      waterContent: 0,
      stoneContent: 0,
      actualAmount: 0,
      designValue: 0,
      storageLocation: '',
    };
    
    onUpdate({
      ...mixRatio,
      items: [...mixRatio.items, newItem]
    });
  };

  const handleRemoveItem = (itemId: string) => {
    const updatedItems = mixRatio.items.filter(item => item.id !== itemId);
    onUpdate({
      ...mixRatio,
      items: updatedItems
    });
  };

  const adjustValue = (itemId: string, field: keyof MixRatioItem, delta: number) => {
    const item = mixRatio.items.find(i => i.id === itemId);
    if (!item) return;
    
    const currentValue = Number(item[field]) || 0;
    const newValue = Math.max(0, currentValue + delta);
    handleItemChange(itemId, field, newValue);
  };

  const getStorageDisplayValue = (item: MixRatioItem) => {
    switch (storageDisplayMode) {
      case 'silo':
        return item.storageLocation || '未设置';
      case 'station':
        return item.stationName || '未设置';
      case 'code':
        return item.stationCode || '未设置';
      default:
        return item.storageLocation || '未设置';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Badge variant="outline">三</Badge>
            配比
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Button size="sm" variant="outline" onClick={onOpenQualityStandard}>
              <CheckSquare className="mr-2 h-4 w-4" />
              检查标准
            </Button>
            
            <Button size="sm" variant="outline">
              <FileText className="mr-2 h-4 w-4" />
              应用配比通知单内容到当前
            </Button>
            
            <Button size="sm" variant="outline">
              选择配比
            </Button>
            
            <Button size="sm" variant="outline">
              <Save className="mr-2 h-4 w-4" />
              保存为备选配比
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* 存储位置显示模式切换 */}
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">材料存储位置显示:</span>
            <Select value={storageDisplayMode} onValueChange={(value: any) => setStorageDisplayMode(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="silo">罐名</SelectItem>
                <SelectItem value="station">站名</SelectItem>
                <SelectItem value="code">站号</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 配比表格 */}
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/50">
                  <TableHead className="w-24">材料名称</TableHead>
                  <TableHead className="w-32">规格</TableHead>
                  <TableHead className="w-24">理论量</TableHead>
                  <TableHead className="w-24">含水率%</TableHead>
                  <TableHead className="w-24">含石率%</TableHead>
                  <TableHead className="w-24">实际量</TableHead>
                  <TableHead className="w-24">设计值</TableHead>
                  <TableHead className="w-32">材料存储位置</TableHead>
                  <TableHead className="w-20">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mixRatio.items.map((item) => (
                  <TableRow key={item.id}>
                    {/* 材料名称 */}
                    <TableCell>
                      <Select 
                        value={item.materialId} 
                        onValueChange={(value) => {
                          const material = materials.find(m => m.id === value);
                          if (material) {
                            handleItemChange(item.id, 'materialId', value);
                            handleItemChange(item.id, 'materialName', material.name);
                            handleItemChange(item.id, 'specification', material.specification);
                          }
                        }}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="选择材料" />
                        </SelectTrigger>
                        <SelectContent>
                          {materials.map((material) => (
                            <SelectItem key={material.id} value={material.id}>
                              {material.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </TableCell>

                    {/* 规格 */}
                    <TableCell>
                      <Input
                        value={item.specification}
                        onChange={(e) => handleItemChange(item.id, 'specification', e.target.value)}
                        className="h-8"
                      />
                    </TableCell>

                    {/* 理论量 */}
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-6 w-6 p-0"
                          onClick={() => adjustValue(item.id, 'theoreticalAmount', -1)}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <Input
                          type="number"
                          value={item.theoreticalAmount}
                          onChange={(e) => handleItemChange(item.id, 'theoreticalAmount', Number(e.target.value))}
                          className="h-8 text-center"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-6 w-6 p-0"
                          onClick={() => adjustValue(item.id, 'theoreticalAmount', 1)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>

                    {/* 含水率 */}
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-6 w-6 p-0"
                          onClick={() => adjustValue(item.id, 'waterContent', -0.1)}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <Input
                          type="number"
                          step="0.1"
                          value={item.waterContent}
                          onChange={(e) => handleItemChange(item.id, 'waterContent', Number(e.target.value))}
                          className="h-8 text-center"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-6 w-6 p-0"
                          onClick={() => adjustValue(item.id, 'waterContent', 0.1)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>

                    {/* 含石率 */}
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-6 w-6 p-0"
                          onClick={() => adjustValue(item.id, 'stoneContent', -0.1)}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <Input
                          type="number"
                          step="0.1"
                          value={item.stoneContent}
                          onChange={(e) => handleItemChange(item.id, 'stoneContent', Number(e.target.value))}
                          className="h-8 text-center"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-6 w-6 p-0"
                          onClick={() => adjustValue(item.id, 'stoneContent', 0.1)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>

                    {/* 实际量 */}
                    <TableCell>
                      <Input
                        type="number"
                        value={item.actualAmount}
                        onChange={(e) => handleItemChange(item.id, 'actualAmount', Number(e.target.value))}
                        className="h-8"
                      />
                    </TableCell>

                    {/* 设计值 */}
                    <TableCell>
                      <Input
                        type="number"
                        value={item.designValue}
                        onChange={(e) => handleItemChange(item.id, 'designValue', Number(e.target.value))}
                        className="h-8"
                      />
                    </TableCell>

                    {/* 材料存储位置 */}
                    <TableCell>
                      <Select 
                        value={item.siloId || ''} 
                        onValueChange={(value) => {
                          const silo = silos.find(s => s.id === value);
                          if (silo) {
                            handleItemChange(item.id, 'siloId', value);
                            handleItemChange(item.id, 'storageLocation', silo.name);
                          }
                        }}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="选择料仓" />
                        </SelectTrigger>
                        <SelectContent>
                          {silos.map((silo) => (
                            <SelectItem key={silo.id} value={silo.id}>
                              {getStorageDisplayValue({ ...item, storageLocation: silo.name })}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </TableCell>

                    {/* 操作 */}
                    <TableCell>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleRemoveItem(item.id)}
                        className="h-6 w-6 p-0"
                      >
                        <Minus className="h-3 w-3" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* 添加材料按钮 */}
          <div className="flex justify-start">
            <Button size="sm" variant="outline" onClick={handleAddItem}>
              <Plus className="mr-2 h-4 w-4" />
              添加材料
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
