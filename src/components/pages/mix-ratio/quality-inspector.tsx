'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Target,
  Thermometer,
  Droplets,
  Zap,
  TrendingUp,
  Clock
} from 'lucide-react';

interface QualityCheckResult {
  category: string;
  item: string;
  status: 'pass' | 'warning' | 'fail';
  value: number;
  target: number;
  tolerance: number;
  unit: string;
  description: string;
}

interface QualityInspectorProps {
  parameters: {
    density: number;
    waterRatio: number;
    waterAmount: number;
    sandRatio: number;
    additiveRatio: number;
    flyashRatio: number;
    totalWeight: number;
    targetStrength: number;
    slump: number;
    temperature: number;
  };
  materials: Array<{
    name: string;
    type: string;
    amount: number;
    waterContent: number;
  }>;
  onQualityChange: (score: number, issues: string[]) => void;
}

export function QualityInspector({ parameters, materials, onQualityChange }: QualityInspectorProps) {
  const [qualityResults, setQualityResults] = useState<QualityCheckResult[]>([]);
  const [overallScore, setOverallScore] = useState(0);
  const [isInspecting, setIsInspecting] = useState(false);

  // 质量检验规则
  const qualityRules = {
    waterRatio: { min: 0.25, max: 0.65, optimal: [0.35, 0.55] },
    sandRatio: { min: 25, max: 45, optimal: [30, 40] },
    additiveRatio: { min: 0.5, max: 3.0, optimal: [0.8, 2.5] },
    flyashRatio: { min: 0, max: 30, optimal: [10, 25] },
    density: { min: 2.2, max: 2.5, optimal: [2.3, 2.4] },
    slump: { min: 50, max: 250, optimal: [120, 200] },
    temperature: { min: 5, max: 35, optimal: [15, 25] }
  };

  const performQualityInspection = async () => {
    setIsInspecting(true);
    
    // 模拟检验延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const results: QualityCheckResult[] = [];
    
    // 水胶比检验
    const waterRatioRule = qualityRules.waterRatio;
    const waterRatioStatus = 
      parameters.waterRatio < waterRatioRule.min || parameters.waterRatio > waterRatioRule.max ? 'fail' :
      parameters.waterRatio < waterRatioRule.optimal[0] || parameters.waterRatio > waterRatioRule.optimal[1] ? 'warning' : 'pass';
    
    results.push({
      category: '配比参数',
      item: '水胶比',
      status: waterRatioStatus,
      value: parameters.waterRatio,
      target: (waterRatioRule.optimal[0] + waterRatioRule.optimal[1]) / 2,
      tolerance: 0.05,
      unit: '',
      description: '水胶比直接影响混凝土强度和耐久性'
    });

    // 砂率检验
    const sandRatioRule = qualityRules.sandRatio;
    const sandRatioStatus = 
      parameters.sandRatio < sandRatioRule.min || parameters.sandRatio > sandRatioRule.max ? 'fail' :
      parameters.sandRatio < sandRatioRule.optimal[0] || parameters.sandRatio > sandRatioRule.optimal[1] ? 'warning' : 'pass';
    
    results.push({
      category: '配比参数',
      item: '砂率',
      status: sandRatioStatus,
      value: parameters.sandRatio,
      target: (sandRatioRule.optimal[0] + sandRatioRule.optimal[1]) / 2,
      tolerance: 3,
      unit: '%',
      description: '砂率影响混凝土的工作性和强度'
    });

    // 外加剂掺量检验
    const additiveRule = qualityRules.additiveRatio;
    const additiveStatus = 
      parameters.additiveRatio < additiveRule.min || parameters.additiveRatio > additiveRule.max ? 'fail' :
      parameters.additiveRatio < additiveRule.optimal[0] || parameters.additiveRatio > additiveRule.optimal[1] ? 'warning' : 'pass';
    
    results.push({
      category: '配比参数',
      item: '外加剂掺量',
      status: additiveStatus,
      value: parameters.additiveRatio,
      target: (additiveRule.optimal[0] + additiveRule.optimal[1]) / 2,
      tolerance: 0.3,
      unit: '%',
      description: '外加剂掺量影响工作性和经济性'
    });

    // 密度检验
    const densityRule = qualityRules.density;
    const densityStatus = 
      parameters.density < densityRule.min || parameters.density > densityRule.max ? 'fail' :
      parameters.density < densityRule.optimal[0] || parameters.density > densityRule.optimal[1] ? 'warning' : 'pass';
    
    results.push({
      category: '物理性能',
      item: '密度',
      status: densityStatus,
      value: parameters.density,
      target: (densityRule.optimal[0] + densityRule.optimal[1]) / 2,
      tolerance: 0.05,
      unit: 't/m³',
      description: '密度反映混凝土的密实程度'
    });

    // 坍落度检验
    const slumpRule = qualityRules.slump;
    const slumpStatus = 
      parameters.slump < slumpRule.min || parameters.slump > slumpRule.max ? 'fail' :
      parameters.slump < slumpRule.optimal[0] || parameters.slump > slumpRule.optimal[1] ? 'warning' : 'pass';
    
    results.push({
      category: '工作性能',
      item: '坍落度',
      status: slumpStatus,
      value: parameters.slump,
      target: (slumpRule.optimal[0] + slumpRule.optimal[1]) / 2,
      tolerance: 20,
      unit: 'mm',
      description: '坍落度反映混凝土的流动性'
    });

    // 温度检验
    const tempRule = qualityRules.temperature;
    const tempStatus = 
      parameters.temperature < tempRule.min || parameters.temperature > tempRule.max ? 'fail' :
      parameters.temperature < tempRule.optimal[0] || parameters.temperature > tempRule.optimal[1] ? 'warning' : 'pass';
    
    results.push({
      category: '环境条件',
      item: '环境温度',
      status: tempStatus,
      value: parameters.temperature,
      target: (tempRule.optimal[0] + tempRule.optimal[1]) / 2,
      tolerance: 5,
      unit: '°C',
      description: '温度影响混凝土的凝结和强度发展'
    });

    // 材料配比检验
    const cementMaterial = materials.find(m => m.name.includes('水泥'));
    const waterMaterial = materials.find(m => m.type === 'water');
    
    if (cementMaterial && waterMaterial) {
      const actualWaterRatio = waterMaterial.amount / cementMaterial.amount;
      const ratioStatus = Math.abs(actualWaterRatio - parameters.waterRatio) > 0.02 ? 'warning' : 'pass';
      
      results.push({
        category: '材料配比',
        item: '实际水胶比',
        status: ratioStatus,
        value: actualWaterRatio,
        target: parameters.waterRatio,
        tolerance: 0.02,
        unit: '',
        description: '实际材料用量计算的水胶比'
      });
    }

    // 计算总体评分
    const passCount = results.filter(r => r.status === 'pass').length;
    const warningCount = results.filter(r => r.status === 'warning').length;
    const failCount = results.filter(r => r.status === 'fail').length;
    
    const score = Math.round(
      (passCount * 100 + warningCount * 70 + failCount * 30) / results.length
    );

    setQualityResults(results);
    setOverallScore(score);
    setIsInspecting(false);

    // 收集问题
    const issues = results
      .filter(r => r.status !== 'pass')
      .map(r => `${r.item}: ${r.status === 'fail' ? '不合格' : '需注意'}`);
    
    onQualityChange(score, issues);
  };

  useEffect(() => {
    performQualityInspection();
  }, [parameters, materials]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'fail': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'fail': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 90) return '优秀';
    if (score >= 80) return '良好';
    if (score >= 70) return '合格';
    if (score >= 60) return '基本合格';
    return '不合格';
  };

  const categoryGroups = qualityResults.reduce((groups, result) => {
    if (!groups[result.category]) {
      groups[result.category] = [];
    }
    groups[result.category].push(result);
    return groups;
  }, {} as Record<string, QualityCheckResult[]>);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case '配比参数': return <Target className="h-4 w-4" />;
      case '物理性能': return <Zap className="h-4 w-4" />;
      case '工作性能': return <Droplets className="h-4 w-4" />;
      case '环境条件': return <Thermometer className="h-4 w-4" />;
      case '材料配比': return <TrendingUp className="h-4 w-4" />;
      default: return <Shield className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-4">
      {/* 总体评分 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <Shield className="h-5 w-5" />
            质量检验报告
            {isInspecting && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4 animate-spin" />
                检验中...
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className={`text-3xl font-bold ${getScoreColor(overallScore)}`}>
                {overallScore}
              </div>
              <div className="text-sm text-muted-foreground">
                综合评分 ({getScoreLabel(overallScore)})
              </div>
            </div>
            <div className="text-right">
              <Progress value={overallScore} className="w-32 h-3 mb-2" />
              <div className="text-xs text-muted-foreground">
                {qualityResults.filter(r => r.status === 'pass').length}/{qualityResults.length} 项通过
              </div>
            </div>
          </div>

          {overallScore < 70 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                质量评分偏低，建议检查并调整配比参数后重新计算
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 分类检验结果 */}
      <div className="space-y-3">
        {Object.entries(categoryGroups).map(([category, results]) => (
          <Card key={category}>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2">
                {getCategoryIcon(category)}
                {category}
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                {results.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded border">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(result.status)}
                      <div>
                        <div className="font-medium text-sm">{result.item}</div>
                        <div className="text-xs text-muted-foreground">
                          {result.description}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(result.status)}>
                          {result.status === 'pass' && '通过'}
                          {result.status === 'warning' && '警告'}
                          {result.status === 'fail' && '不合格'}
                        </Badge>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        实际: {result.value}{result.unit} | 
                        目标: {result.target}{result.unit}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 重新检验按钮 */}
      <div className="flex justify-center">
        <Button 
          variant="outline" 
          onClick={performQualityInspection}
          disabled={isInspecting}
        >
          <Shield className="mr-2 h-4 w-4" />
          重新检验
        </Button>
      </div>
    </div>
  );
}
