'use client';

import React, { useMemo } from 'react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  AlertTriangle, 
  CheckCircle2, 
  Warehouse, 
  TrendingDown,
  TrendingUp,
  AlertCircle
} from 'lucide-react';
import type { Material, SiloInfo, MixRatioItem } from '@/types/mixRatio';

export interface SiloStatus {
  id: string;
  name: string;
  materialType: string;
  capacity: number;
  current: number;
  reserved: number; // 已预留的量
  available: number; // 可用量
  utilizationRate: number; // 利用率
  status: 'normal' | 'warning' | 'critical' | 'empty';
  warnings: string[];
}

export interface SiloLinkageResult {
  isValid: boolean;
  warnings: string[];
  suggestions: string[];
  siloStatuses: SiloStatus[];
  totalCapacityUtilization: number;
}

interface SiloLinkageManagerProps {
  materials: Material[];
  silos: SiloInfo[];
  mixRatioItems: MixRatioItem[];
  selectedStation: string;
  targetVolume?: number;
  onSiloStatusChange?: (statuses: SiloStatus[]) => void;
}

export function SiloLinkageManager({
  materials,
  silos,
  mixRatioItems,
  selectedStation,
  targetVolume = 1,
  onSiloStatusChange
}: SiloLinkageManagerProps) {

  // 计算料仓状态和联动结果
  const linkageResult = useMemo(() => {
    return calculateSiloLinkage({
      materials,
      silos,
      mixRatioItems,
      selectedStation,
      targetVolume
    });
  }, [materials, silos, mixRatioItems, selectedStation, targetVolume]);

  // 通知状态变化
  React.useEffect(() => {
    onSiloStatusChange?.(linkageResult.siloStatuses);
  }, [linkageResult.siloStatuses, onSiloStatusChange]);

  const getStatusColor = (status: SiloStatus['status']) => {
    switch (status) {
      case 'normal': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      case 'empty': return 'text-gray-400';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: SiloStatus['status']) => {
    switch (status) {
      case 'normal': return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'critical': return <AlertCircle className="h-4 w-4 text-red-600" />;
      case 'empty': return <TrendingDown className="h-4 w-4 text-gray-400" />;
      default: return <Warehouse className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusLabel = (status: SiloStatus['status']) => {
    switch (status) {
      case 'normal': return '正常';
      case 'warning': return '警告';
      case 'critical': return '严重';
      case 'empty': return '空仓';
      default: return '未知';
    }
  };

  return (
    <div className="space-y-4">
      {/* 总体状态 */}
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">料仓联动状态</h3>
        <Badge variant={linkageResult.isValid ? 'default' : 'destructive'}>
          {linkageResult.isValid ? '正常' : '异常'}
        </Badge>
      </div>

      {/* 总体容量利用率 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between text-xs">
          <span className="text-muted-foreground">总体容量利用率</span>
          <span className="font-medium">
            {Math.round(linkageResult.totalCapacityUtilization)}%
          </span>
        </div>
        <Progress value={linkageResult.totalCapacityUtilization} className="h-2" />
      </div>

      {/* 料仓状态列表 */}
      <div className="space-y-2">
        {linkageResult.siloStatuses.map((silo) => (
          <div key={silo.id} className="border rounded-lg p-3 space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getStatusIcon(silo.status)}
                <span className="text-sm font-medium">{silo.name}</span>
                <Badge variant="outline" className="text-xs">
                  {silo.materialType}
                </Badge>
              </div>
              <Badge variant={silo.status === 'normal' ? 'default' : 'destructive'} className="text-xs">
                {getStatusLabel(silo.status)}
              </Badge>
            </div>

            {/* 容量信息 */}
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div>
                <span className="text-muted-foreground">容量:</span>
                <div className="font-medium">{silo.capacity}t</div>
              </div>
              <div>
                <span className="text-muted-foreground">当前:</span>
                <div className="font-medium">{silo.current}t</div>
              </div>
              <div>
                <span className="text-muted-foreground">可用:</span>
                <div className={`font-medium ${silo.available < silo.reserved ? 'text-red-600' : 'text-green-600'}`}>
                  {silo.available}t
                </div>
              </div>
            </div>

            {/* 利用率进度条 */}
            <div className="space-y-1">
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">利用率</span>
                <span className={`font-medium ${getStatusColor(silo.status)}`}>
                  {Math.round(silo.utilizationRate)}%
                </span>
              </div>
              <Progress 
                value={silo.utilizationRate} 
                className="h-1.5"
              />
            </div>

            {/* 预留量信息 */}
            {silo.reserved > 0 && (
              <div className="text-xs text-muted-foreground">
                预留: {silo.reserved}t (本次配比需求)
              </div>
            )}

            {/* 警告信息 */}
            {silo.warnings.length > 0 && (
              <div className="space-y-1">
                {silo.warnings.map((warning, index) => (
                  <div key={index} className="flex items-start gap-1 text-xs text-yellow-700 bg-yellow-50 p-1 rounded">
                    <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <span>{warning}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 全局警告 */}
      {linkageResult.warnings.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              {linkageResult.warnings.map((warning, index) => (
                <div key={index}>{warning}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* 优化建议 */}
      {linkageResult.suggestions.length > 0 && (
        <Alert>
          <TrendingUp className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <div className="font-medium">优化建议:</div>
              {linkageResult.suggestions.map((suggestion, index) => (
                <div key={index} className="text-sm">• {suggestion}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

/**
 * 计算料仓联动状态
 */
function calculateSiloLinkage({
  materials,
  silos,
  mixRatioItems,
  selectedStation,
  targetVolume
}: {
  materials: Material[];
  silos: SiloInfo[];
  mixRatioItems: MixRatioItem[];
  selectedStation: string;
  targetVolume: number;
}): SiloLinkageResult {
  const warnings: string[] = [];
  const suggestions: string[] = [];
  const siloStatuses: SiloStatus[] = [];

  // 筛选当前站点的料仓
  const stationSilos = silos.filter(s => s.stationId === selectedStation && s.isEnabled);

  // 计算每个料仓的需求量
  const siloRequirements = new Map<string, number>();
  
  mixRatioItems.forEach(item => {
    if (item.siloId) {
      const currentRequirement = siloRequirements.get(item.siloId) || 0;
      const itemRequirement = (item.theoreticalAmount * targetVolume) / 1000; // kg转t
      siloRequirements.set(item.siloId, currentRequirement + itemRequirement);
    }
  });

  let totalCapacity = 0;
  let totalUsed = 0;

  // 计算每个料仓的状态
  stationSilos.forEach(silo => {
    // 根据料仓类型设置不同的容量
    const mockCapacity = getSiloCapacityByType(silo.materialType);
    const mockCurrent = Math.random() * (mockCapacity * 0.8) + (mockCapacity * 0.1); // 模拟当前库存
    const reserved = siloRequirements.get(silo.id) || 0;
    const available = Math.max(0, mockCurrent - reserved);
    const utilizationRate = (mockCurrent / mockCapacity) * 100;

    totalCapacity += mockCapacity;
    totalUsed += mockCurrent;

    const warnings: string[] = [];
    let status: SiloStatus['status'] = 'normal';

    // 状态判断
    if (mockCurrent <= 0) {
      status = 'empty';
      warnings.push('料仓已空');
    } else if (available < reserved) {
      status = 'critical';
      warnings.push(`库存不足，需要${reserved}t，仅有${mockCurrent}t`);
    } else if (utilizationRate > 90) {
      status = 'warning';
      warnings.push('库存即将用完');
    } else if (utilizationRate < 10) {
      status = 'warning';
      warnings.push('库存过低');
    }

    siloStatuses.push({
      id: silo.id,
      name: silo.name,
      materialType: silo.materialType,
      capacity: mockCapacity,
      current: Math.round(mockCurrent * 10) / 10,
      reserved: Math.round(reserved * 10) / 10,
      available: Math.round(available * 10) / 10,
      utilizationRate: Math.round(utilizationRate * 10) / 10,
      status,
      warnings
    });
  });

  // 全局检查
  const criticalSilos = siloStatuses.filter(s => s.status === 'critical');
  const emptySilos = siloStatuses.filter(s => s.status === 'empty');

  if (criticalSilos.length > 0) {
    warnings.push(`${criticalSilos.length}个料仓库存严重不足`);
  }

  if (emptySilos.length > 0) {
    warnings.push(`${emptySilos.length}个料仓已空`);
  }

  // 优化建议
  if (siloStatuses.some(s => s.utilizationRate < 20)) {
    suggestions.push('建议及时补充库存较低的料仓');
  }

  if (siloStatuses.some(s => s.utilizationRate > 85)) {
    suggestions.push('建议优化料仓配置，避免库存过满');
  }

  const totalCapacityUtilization = totalCapacity > 0 ? (totalUsed / totalCapacity) * 100 : 0;
  const isValid = criticalSilos.length === 0 && emptySilos.length === 0;

  return {
    isValid,
    warnings,
    suggestions,
    siloStatuses,
    totalCapacityUtilization: Math.round(totalCapacityUtilization * 10) / 10
  };
}

/**
 * 根据材料类型获取料仓容量
 */
function getSiloCapacityByType(materialType: string): number {
  switch (materialType) {
    case 'cement':
      return 200; // 水泥仓容量较大
    case 'water':
      return 50;  // 水箱容量中等
    case 'sand':
    case 'stone':
      return 300; // 骨料仓容量最大
    case 'additive':
      return 20;  // 外加剂容量较小
    default:
      return 100; // 默认容量
  }
}

/**
 * 检查料仓容量是否足够
 */
export function checkSiloCapacity(
  silos: SiloInfo[],
  requirements: Map<string, number>
): { isValid: boolean; warnings: string[] } {
  const warnings: string[] = [];
  let isValid = true;

  requirements.forEach((required, siloId) => {
    const silo = silos.find(s => s.id === siloId);
    if (!silo) {
      warnings.push(`料仓 ${siloId} 不存在`);
      isValid = false;
      return;
    }

    const capacity = getSiloCapacityByType(silo.materialType);
    const mockCurrent = Math.random() * (capacity * 0.8) + (capacity * 0.1);

    if (mockCurrent < required) {
      warnings.push(`${silo.name} 库存不足：需要 ${required}t，仅有 ${Math.round(mockCurrent * 10) / 10}t`);
      isValid = false;
    } else if ((mockCurrent - required) < capacity * 0.1) {
      warnings.push(`${silo.name} 使用后库存将过低`);
    }
  });

  return { isValid, warnings };
}
