'use client';

import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle2, 
  AlertTriangle, 
  XCircle, 
  Info,
  TrendingUp
} from 'lucide-react';
import type { MixRatio, MixCalculationParams, MixRatioItem } from '@/types/mixRatio';

export interface ValidationRule {
  id: string;
  name: string;
  description: string;
  severity: 'error' | 'warning' | 'info';
  check: (mixRatio: MixRatio) => ValidationResult;
}

export interface ValidationResult {
  isValid: boolean;
  message: string;
  suggestion?: string;
  value?: number;
  range?: { min: number; max: number };
}

export interface MixRatioValidationResult {
  isValid: boolean;
  score: number; // 0-100
  errors: Array<ValidationRule & { result: ValidationResult }>;
  warnings: Array<ValidationRule & { result: ValidationResult }>;
  infos: Array<ValidationRule & { result: ValidationResult }>;
}

interface MixRatioValidatorProps {
  mixRatio: MixRatio;
  onValidationChange?: (result: MixRatioValidationResult) => void;
  showDetails?: boolean;
}

export function MixRatioValidator({ 
  mixRatio, 
  onValidationChange, 
  showDetails = true 
}: MixRatioValidatorProps) {
  
  const validationResult = React.useMemo(() => {
    return validateMixRatio(mixRatio);
  }, [mixRatio]);

  React.useEffect(() => {
    onValidationChange?.(validationResult);
  }, [validationResult, onValidationChange]);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return '优秀';
    if (score >= 60) return '良好';
    if (score >= 40) return '一般';
    return '需要改进';
  };

  if (!showDetails && validationResult.isValid) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* 总体评分 */}
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">配比验证</h3>
        <div className="flex items-center space-x-2">
          <span className={`text-sm font-medium ${getScoreColor(validationResult.score)}`}>
            {getScoreLabel(validationResult.score)}
          </span>
          <Badge variant={validationResult.isValid ? 'default' : 'destructive'}>
            {validationResult.score}分
          </Badge>
        </div>
      </div>

      {/* 评分进度条 */}
      <div className="space-y-1">
        <Progress value={validationResult.score} className="h-2" />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>0</span>
          <span>50</span>
          <span>100</span>
        </div>
      </div>

      {showDetails && (
        <div className="space-y-3">
          {/* 错误信息 */}
          {validationResult.errors.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-red-600 flex items-center gap-2">
                <XCircle className="h-4 w-4" />
                错误 ({validationResult.errors.length})
              </h4>
              {validationResult.errors.map((error, index) => (
                <Alert key={index} variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <div className="font-medium">{error.name}</div>
                      <div className="text-sm">{error.result.message}</div>
                      {error.result.suggestion && (
                        <div className="text-sm text-muted-foreground">
                          建议: {error.result.suggestion}
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          )}

          {/* 警告信息 */}
          {validationResult.warnings.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-yellow-600 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                警告 ({validationResult.warnings.length})
              </h4>
              {validationResult.warnings.map((warning, index) => (
                <Alert key={index}>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <div className="font-medium">{warning.name}</div>
                      <div className="text-sm">{warning.result.message}</div>
                      {warning.result.suggestion && (
                        <div className="text-sm text-muted-foreground">
                          建议: {warning.result.suggestion}
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          )}

          {/* 信息提示 */}
          {validationResult.infos.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-blue-600 flex items-center gap-2">
                <Info className="h-4 w-4" />
                提示 ({validationResult.infos.length})
              </h4>
              {validationResult.infos.map((info, index) => (
                <Alert key={index}>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <div className="font-medium">{info.name}</div>
                      <div className="text-sm">{info.result.message}</div>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          )}

          {/* 全部通过 */}
          {validationResult.isValid && validationResult.errors.length === 0 && validationResult.warnings.length === 0 && (
            <Alert>
              <CheckCircle2 className="h-4 w-4" />
              <AlertDescription>
                配比验证通过，所有参数都在合理范围内。
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * 配比验证规则
 */
const validationRules: ValidationRule[] = [
  {
    id: 'water-cement-ratio',
    name: '水胶比检查',
    description: '水胶比应在合理范围内',
    severity: 'error',
    check: (mixRatio) => {
      const ratio = mixRatio.calculationParams.waterCementRatio;
      if (ratio < 0.2 || ratio > 0.8) {
        return {
          isValid: false,
          message: `水胶比 ${ratio} 超出合理范围 (0.2-0.8)`,
          suggestion: '调整用水量或胶凝材料用量',
          value: ratio,
          range: { min: 0.2, max: 0.8 }
        };
      }
      return { isValid: true, message: '水胶比正常' };
    }
  },
  {
    id: 'sand-ratio',
    name: '砂率检查',
    description: '砂率应在合理范围内',
    severity: 'warning',
    check: (mixRatio) => {
      const ratio = mixRatio.calculationParams.sandRatio;
      if (ratio < 25 || ratio > 45) {
        return {
          isValid: false,
          message: `砂率 ${ratio}% 超出推荐范围 (25-45%)`,
          suggestion: '调整砂石比例',
          value: ratio,
          range: { min: 25, max: 45 }
        };
      }
      return { isValid: true, message: '砂率正常' };
    }
  },
  {
    id: 'density-check',
    name: '密度检查',
    description: '混凝土密度应在合理范围内',
    severity: 'warning',
    check: (mixRatio) => {
      const density = mixRatio.calculationParams.density;
      if (density < 2.0 || density > 2.8) {
        return {
          isValid: false,
          message: `密度 ${density} t/m³ 超出正常范围 (2.0-2.8)`,
          suggestion: '检查材料配比是否合理',
          value: density,
          range: { min: 2.0, max: 2.8 }
        };
      }
      return { isValid: true, message: '密度正常' };
    }
  },
  {
    id: 'additive-ratio',
    name: '外加剂掺量检查',
    description: '外加剂掺量应适中',
    severity: 'warning',
    check: (mixRatio) => {
      const ratio = mixRatio.calculationParams.additiveRatio;
      if (ratio > 5) {
        return {
          isValid: false,
          message: `外加剂掺量 ${ratio}% 过高`,
          suggestion: '降低外加剂掺量或检查配比',
          value: ratio,
          range: { min: 0, max: 5 }
        };
      }
      return { isValid: true, message: '外加剂掺量正常' };
    }
  },
  {
    id: 'material-completeness',
    name: '材料完整性检查',
    description: '检查必要材料是否齐全',
    severity: 'error',
    check: (mixRatio) => {
      const materials = mixRatio.items;
      const hasWater = materials.some(m => m.materialName.includes('水'));
      const hasCement = materials.some(m => m.materialName.includes('水泥'));
      const hasSand = materials.some(m => m.materialName.includes('砂') || m.materialName.includes('沙'));
      const hasStone = materials.some(m => m.materialName.includes('石'));

      const missing = [];
      if (!hasWater) missing.push('水');
      if (!hasCement) missing.push('水泥');
      if (!hasSand) missing.push('砂');
      if (!hasStone) missing.push('石子');

      if (missing.length > 0) {
        return {
          isValid: false,
          message: `缺少必要材料: ${missing.join('、')}`,
          suggestion: '添加缺少的材料'
        };
      }
      return { isValid: true, message: '材料齐全' };
    }
  }
];

/**
 * 执行配比验证
 */
function validateMixRatio(mixRatio: MixRatio): MixRatioValidationResult {
  const errors: Array<ValidationRule & { result: ValidationResult }> = [];
  const warnings: Array<ValidationRule & { result: ValidationResult }> = [];
  const infos: Array<ValidationRule & { result: ValidationResult }> = [];

  let totalScore = 100;
  let validRules = 0;

  validationRules.forEach(rule => {
    const result = rule.check(mixRatio);
    
    if (!result.isValid) {
      const ruleWithResult = { ...rule, result };
      
      switch (rule.severity) {
        case 'error':
          errors.push(ruleWithResult);
          totalScore -= 30; // 错误扣30分
          break;
        case 'warning':
          warnings.push(ruleWithResult);
          totalScore -= 15; // 警告扣15分
          break;
        case 'info':
          infos.push(ruleWithResult);
          totalScore -= 5; // 信息扣5分
          break;
      }
    } else {
      validRules++;
    }
  });

  const score = Math.max(0, Math.min(100, totalScore));
  const isValid = errors.length === 0;

  return {
    isValid,
    score,
    errors,
    warnings,
    infos
  };
}
