'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  HelpCircle, 
  X, 
  ChevronRight, 
  Calculator, 
  Database, 
  Warehouse,
  FileText,
  CheckCircle2,
  ArrowRight
} from 'lucide-react';

interface OperationStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  tips?: string[];
  shortcuts?: string[];
}

interface OperationGuideProps {
  isOpen: boolean;
  onClose: () => void;
  currentStep?: string;
}

export function OperationGuide({ isOpen, onClose, currentStep }: OperationGuideProps) {
  const [selectedStep, setSelectedStep] = useState<string>(currentStep || 'station-selection');

  const operationSteps: OperationStep[] = [
    {
      id: 'station-selection',
      title: '选择搅拌站',
      description: '首先选择要配置配比的搅拌站，系统会自动加载对应的材料和料仓信息。',
      icon: <Warehouse className="h-5 w-5" />,
      tips: [
        '不同搅拌站的材料配置可能不同',
        '切换搅拌站会重新加载相关数据',
        '确保选择的搅拌站状态为"运行中"'
      ],
      shortcuts: ['Ctrl+S: 快速切换搅拌站']
    },
    {
      id: 'task-info',
      title: '填写任务信息',
      description: '输入工程基本信息，包括强度等级、施工部位、坍落度等关键参数。',
      icon: <FileText className="h-5 w-5" />,
      tips: [
        '强度等级决定了配比的基础参数',
        '坍落度影响工作性要求',
        '施工部位影响配比选择'
      ],
      shortcuts: ['Tab: 快速切换输入框']
    },
    {
      id: 'material-selection',
      title: '选择配比材料',
      description: '在配比表格中选择所需材料，系统会自动关联对应的料仓。',
      icon: <Database className="h-5 w-5" />,
      tips: [
        '材料选择会自动关联料仓',
        '可以搜索和筛选材料',
        '注意检查材料规格是否正确'
      ],
      shortcuts: [
        'Ctrl+A: 添加新材料行',
        'Delete: 删除选中材料'
      ]
    },
    {
      id: 'parameter-calculation',
      title: '设置计算参数',
      description: '输入密度、水胶比、砂率等关键参数，或使用反算功能从材料用量推算参数。',
      icon: <Calculator className="h-5 w-5" />,
      tips: [
        '可以使用微调按钮精确调整数值',
        '反算功能可以从材料用量推算参数',
        '注意检查参数的合理性'
      ],
      shortcuts: [
        'Ctrl+R: 执行反算',
        '↑↓: 微调数值',
        'Ctrl+Enter: 应用计算结果'
      ]
    },
    {
      id: 'reverse-calculation',
      title: '配比反算',
      description: '当已知材料用量时，可以使用反算功能推算出对应的配比参数。',
      icon: <Calculator className="h-5 w-5" />,
      tips: [
        '确保材料用量数据准确',
        '反算结果会显示可信度',
        '注意查看警告和建议信息'
      ],
      shortcuts: ['Ctrl+R: 执行反算']
    },
    {
      id: 'silo-management',
      title: '料仓状态监控',
      description: '实时监控料仓状态，确保库存充足，避免生产中断。',
      icon: <Warehouse className="h-5 w-5" />,
      tips: [
        '红色表示库存不足需要补充',
        '黄色表示库存偏低需要关注',
        '绿色表示库存正常'
      ]
    },
    {
      id: 'save-and-submit',
      title: '保存和提交',
      description: '完成配比设置后，保存配比并提交审核。',
      icon: <CheckCircle2 className="h-5 w-5" />,
      tips: [
        '保存前会自动验证配比合理性',
        '提交后配比状态变为"已发送"',
        '可以选择同步到其他搅拌站'
      ],
      shortcuts: [
        'Ctrl+S: 保存配比',
        'Ctrl+Shift+S: 提交审核'
      ]
    }
  ];

  const currentStepData = operationSteps.find(step => step.id === selectedStep);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl max-h-[80vh] overflow-hidden">
        <CardHeader className="border-b">
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5" />
              配比操作指南
            </span>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </CardTitle>
        </CardHeader>

        <div className="flex h-[60vh]">
          {/* 左侧步骤列表 */}
          <div className="w-80 border-r bg-muted/20 overflow-y-auto">
            <div className="p-4 space-y-2">
              {operationSteps.map((step, index) => (
                <div
                  key={step.id}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedStep === step.id
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-muted'
                  }`}
                  onClick={() => setSelectedStep(step.id)}
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">
                        {index + 1}
                      </Badge>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        {step.icon}
                        <span className="text-sm font-medium truncate">
                          {step.title}
                        </span>
                      </div>
                    </div>
                    <ChevronRight className="h-4 w-4 flex-shrink-0" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 右侧详细内容 */}
          <div className="flex-1 overflow-y-auto">
            {currentStepData && (
              <div className="p-6 space-y-6">
                {/* 步骤标题 */}
                <div className="flex items-center space-x-3">
                  {currentStepData.icon}
                  <h2 className="text-xl font-semibold">{currentStepData.title}</h2>
                </div>

                {/* 步骤描述 */}
                <div className="text-muted-foreground">
                  {currentStepData.description}
                </div>

                {/* 操作提示 */}
                {currentStepData.tips && currentStepData.tips.length > 0 && (
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium">操作提示</h3>
                    <div className="space-y-2">
                      {currentStepData.tips.map((tip, index) => (
                        <div key={index} className="flex items-start space-x-2 text-sm">
                          <ArrowRight className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <span>{tip}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 快捷键 */}
                {currentStepData.shortcuts && currentStepData.shortcuts.length > 0 && (
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium">快捷键</h3>
                    <div className="space-y-2">
                      {currentStepData.shortcuts.map((shortcut, index) => (
                        <div key={index} className="flex items-center space-x-2 text-sm">
                          <Badge variant="outline" className="font-mono text-xs">
                            {shortcut.split(':')[0]}
                          </Badge>
                          <span className="text-muted-foreground">
                            {shortcut.split(':')[1]}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <Separator />

                {/* 导航按钮 */}
                <div className="flex items-center justify-between">
                  <Button
                    variant="outline"
                    onClick={() => {
                      const currentIndex = operationSteps.findIndex(s => s.id === selectedStep);
                      if (currentIndex > 0) {
                        setSelectedStep(operationSteps[currentIndex - 1].id);
                      }
                    }}
                    disabled={operationSteps.findIndex(s => s.id === selectedStep) === 0}
                  >
                    上一步
                  </Button>

                  <span className="text-sm text-muted-foreground">
                    {operationSteps.findIndex(s => s.id === selectedStep) + 1} / {operationSteps.length}
                  </span>

                  <Button
                    onClick={() => {
                      const currentIndex = operationSteps.findIndex(s => s.id === selectedStep);
                      if (currentIndex < operationSteps.length - 1) {
                        setSelectedStep(operationSteps[currentIndex + 1].id);
                      } else {
                        onClose();
                      }
                    }}
                  >
                    {operationSteps.findIndex(s => s.id === selectedStep) === operationSteps.length - 1
                      ? '完成'
                      : '下一步'
                    }
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
}
