'use client';

import React from 'react';
import type { MixCalculationParams, MixRatioItem } from '@/types/mixRatio';

export interface ReverseCalculationInput {
  materials: MixRatioItem[];
  targetVolume?: number; // 目标体积 m³，默认为1
  currentParams?: Partial<MixCalculationParams>;
}

export interface ReverseCalculationResult {
  calculatedParams: MixCalculationParams;
  warnings: string[];
  suggestions: string[];
  isValid: boolean;
  confidence: number; // 0-1，计算结果的可信度
}

/**
 * 配比反算引擎
 * 从材料用量反推配比参数
 */
export class ReverseCalculationEngine {
  
  /**
   * 执行配比反算
   * 支持多种反算场景：
   * 1. 从材料用量反算基础参数（密度、水胶比、砂率）
   * 2. 从外加剂用量反算掺合料比例
   * 3. 验证配比参数一致性和合理性
   */
  static calculate(input: ReverseCalculationInput): ReverseCalculationResult {
    const { materials, targetVolume = 1, currentParams = {} } = input;
    
    const warnings: string[] = [];
    const suggestions: string[] = [];
    let confidence = 1.0;
    
    // 分类材料
    const materialsByType = this.categorizeMaterials(materials);
    
    // 计算基础参数
    const totalWeight = this.calculateTotalWeight(materials);
    const density = totalWeight / 1000; // kg/m³ 转 t/m³
    
    // 计算水胶比
    const waterAmount = this.getMaterialAmount(materialsByType.water);
    const cementAmount = this.getMaterialAmount(materialsByType.cement);
    const flyashAmount = this.getMaterialAmount(materialsByType.flyash);
    const slagAmount = this.getMaterialAmount(materialsByType.slag);
    
    const totalCementitious = cementAmount + flyashAmount + slagAmount;
    const waterCementRatio = totalCementitious > 0 ? waterAmount / totalCementitious : 0.45;
    
    // 计算砂率
    const sandAmount = this.getMaterialAmount(materialsByType.sand);
    const stoneAmount = this.getMaterialAmount(materialsByType.stone);
    const totalAggregate = sandAmount + stoneAmount;
    const sandRatio = totalAggregate > 0 ? (sandAmount / totalAggregate) * 100 : 35;
    
    // 计算各种掺合料比例
    const additiveAmount = this.getMaterialAmount(materialsByType.additive);
    const antifreezeAmount = this.getMaterialAmount(materialsByType.antifreeze);
    const expansionAmount = this.getMaterialAmount(materialsByType.expansion);
    const acceleratorAmount = this.getMaterialAmount(materialsByType.accelerator);
    const ultrafineAmount = this.getMaterialAmount(materialsByType.ultrafine);
    const silicaAmount = this.getMaterialAmount(materialsByType.silica);
    
    const additiveRatio = totalCementitious > 0 ? (additiveAmount / totalCementitious) * 100 : 0;
    const antifreezeRatio = totalCementitious > 0 ? (antifreezeAmount / totalCementitious) * 100 : 0;
    const flyashRatio = totalCementitious > 0 ? (flyashAmount / totalCementitious) * 100 : 0;
    const slagRatio = totalCementitious > 0 ? (slagAmount / totalCementitious) * 100 : 0;
    const expansionRatio = totalCementitious > 0 ? (expansionAmount / totalCementitious) * 100 : 0;
    const acceleratorRatio = totalCementitious > 0 ? (acceleratorAmount / totalCementitious) * 100 : 0;
    const ultrafineRatio = totalAggregate > 0 ? (ultrafineAmount / totalAggregate) * 100 : 0;
    const silicaRatio = totalCementitious > 0 ? (silicaAmount / totalCementitious) * 100 : 0;
    
    // 计算搅拌时间（经验公式）
    const mixingTime = Math.round(60 + (totalWeight - 2000) / 50);
    
    // 验证计算结果
    const validation = this.validateResults({
      density,
      waterCementRatio,
      sandRatio,
      additiveRatio,
      flyashRatio,
      slagRatio,
      totalWeight,
      waterAmount,
      cementAmount,
      totalCementitious,
      totalAggregate
    });
    
    warnings.push(...validation.warnings);
    suggestions.push(...validation.suggestions);
    confidence *= validation.confidence;
    
    const calculatedParams: MixCalculationParams = {
      density: Math.round(density * 100) / 100,
      totalWeight: Math.round(totalWeight),
      mixingTime: Math.max(60, mixingTime),
      waterCementRatio: Math.round(waterCementRatio * 100) / 100,
      waterContent: Math.round(waterAmount),
      sandRatio: Math.round(sandRatio * 10) / 10,
      additiveRatio: Math.round(additiveRatio * 10) / 10,
      antifreezeRatio: Math.round(antifreezeRatio * 10) / 10,
      flyashRatio: Math.round(flyashRatio * 10) / 10,
      slagRatio: Math.round(slagRatio * 10) / 10,
      silicaRatio: Math.round(silicaRatio * 10) / 10,
      expansionRatio: Math.round(expansionRatio * 10) / 10,
      acceleratorRatio: Math.round(acceleratorRatio * 10) / 10,
      ultrafineRatio: Math.round(ultrafineRatio * 10) / 10
    };
    
    return {
      calculatedParams,
      warnings,
      suggestions,
      isValid: validation.isValid,
      confidence: Math.round(confidence * 100) / 100
    };
  }
  
  /**
   * 按类型分类材料
   */
  private static categorizeMaterials(materials: MixRatioItem[]) {
    const categories = {
      water: [] as MixRatioItem[],
      cement: [] as MixRatioItem[],
      sand: [] as MixRatioItem[],
      stone: [] as MixRatioItem[],
      flyash: [] as MixRatioItem[],
      slag: [] as MixRatioItem[],
      additive: [] as MixRatioItem[],
      antifreeze: [] as MixRatioItem[],
      expansion: [] as MixRatioItem[],
      accelerator: [] as MixRatioItem[],
      ultrafine: [] as MixRatioItem[],
      silica: [] as MixRatioItem[],
      other: [] as MixRatioItem[]
    };
    
    materials.forEach(material => {
      const name = material.materialName.toLowerCase();
      if (name.includes('水') && !name.includes('水泥')) {
        categories.water.push(material);
      } else if (name.includes('水泥')) {
        categories.cement.push(material);
      } else if (name.includes('砂') || name.includes('沙')) {
        if (name.includes('超细')) {
          categories.ultrafine.push(material);
        } else {
          categories.sand.push(material);
        }
      } else if (name.includes('石') || name.includes('碎石')) {
        categories.stone.push(material);
      } else if (name.includes('粉煤灰') || name.includes('煤灰')) {
        categories.flyash.push(material);
      } else if (name.includes('矿粉') || name.includes('矿渣')) {
        categories.slag.push(material);
      } else if (name.includes('防冻')) {
        categories.antifreeze.push(material);
      } else if (name.includes('膨胀')) {
        categories.expansion.push(material);
      } else if (name.includes('早强')) {
        categories.accelerator.push(material);
      } else if (name.includes('硅灰') || name.includes('s105')) {
        categories.silica.push(material);
      } else if (name.includes('外加剂') || name.includes('减水')) {
        categories.additive.push(material);
      } else {
        categories.other.push(material);
      }
    });
    
    return categories;
  }
  
  /**
   * 获取某类材料的总用量
   */
  private static getMaterialAmount(materials: MixRatioItem[]): number {
    return materials.reduce((sum, material) => sum + material.theoreticalAmount, 0);
  }
  
  /**
   * 计算总重量
   */
  private static calculateTotalWeight(materials: MixRatioItem[]): number {
    return materials.reduce((sum, material) => sum + material.theoreticalAmount, 0);
  }
  
  /**
   * 验证计算结果
   */
  private static validateResults(data: {
    density: number;
    waterCementRatio: number;
    sandRatio: number;
    additiveRatio: number;
    flyashRatio: number;
    slagRatio: number;
    totalWeight: number;
    waterAmount: number;
    cementAmount: number;
    totalCementitious: number;
    totalAggregate: number;
  }) {
    const warnings: string[] = [];
    const suggestions: string[] = [];
    let confidence = 1.0;
    let isValid = true;
    
    // 密度检查
    if (data.density < 2.0 || data.density > 2.8) {
      warnings.push(`密度 ${data.density} t/m³ 超出正常范围 (2.0-2.8)`);
      confidence *= 0.8;
      if (data.density < 1.5 || data.density > 3.5) {
        isValid = false;
      }
    }
    
    // 水胶比检查
    if (data.waterCementRatio < 0.25 || data.waterCementRatio > 0.8) {
      warnings.push(`水胶比 ${data.waterCementRatio} 超出推荐范围 (0.25-0.8)`);
      confidence *= 0.9;
      if (data.waterCementRatio < 0.2 || data.waterCementRatio > 1.0) {
        isValid = false;
      }
    }
    
    // 砂率检查
    if (data.sandRatio < 25 || data.sandRatio > 45) {
      warnings.push(`砂率 ${data.sandRatio}% 超出推荐范围 (25-45%)`);
      confidence *= 0.9;
    }
    
    // 外加剂掺量检查
    if (data.additiveRatio > 5) {
      warnings.push(`外加剂掺量 ${data.additiveRatio}% 过高`);
      confidence *= 0.9;
    }
    
    // 粉煤灰掺量检查
    if (data.flyashRatio > 30) {
      warnings.push(`粉煤灰掺量 ${data.flyashRatio}% 过高，可能影响早期强度`);
      suggestions.push('建议控制粉煤灰掺量在30%以内');
      confidence *= 0.9;
    }
    
    // 胶凝材料总量检查
    if (data.totalCementitious < 200) {
      warnings.push('胶凝材料总量过低，可能影响强度和耐久性');
      confidence *= 0.8;
    } else if (data.totalCementitious > 600) {
      warnings.push('胶凝材料总量过高，可能导致收缩开裂');
      suggestions.push('建议优化配比，降低胶凝材料用量');
      confidence *= 0.9;
    }
    
    // 用水量检查
    if (data.waterAmount < 120) {
      warnings.push('用水量过低，可能影响工作性');
      confidence *= 0.9;
    } else if (data.waterAmount > 220) {
      warnings.push('用水量过高，可能影响强度和耐久性');
      confidence *= 0.9;
    }
    
    return { warnings, suggestions, confidence, isValid };
  }
  
  /**
   * 比较两组参数的差异
   */
  static compareParameters(
    original: MixCalculationParams,
    calculated: MixCalculationParams
  ): Array<{
    parameter: string;
    original: number;
    calculated: number;
    difference: number;
    percentDifference: number;
    isSignificant: boolean;
  }> {
    const comparisons = [];
    const parameters = [
      { key: 'density', name: '密度', unit: 't/m³', threshold: 0.1 },
      { key: 'waterCementRatio', name: '水胶比', unit: '', threshold: 0.05 },
      { key: 'sandRatio', name: '砂率', unit: '%', threshold: 2 },
      { key: 'additiveRatio', name: '外加剂掺量', unit: '%', threshold: 0.5 },
      { key: 'flyashRatio', name: '粉煤灰掺量', unit: '%', threshold: 2 },
      { key: 'slagRatio', name: '矿粉掺量', unit: '%', threshold: 2 }
    ];
    
    parameters.forEach(param => {
      const originalValue = original[param.key as keyof MixCalculationParams] as number;
      const calculatedValue = calculated[param.key as keyof MixCalculationParams] as number;
      const difference = calculatedValue - originalValue;
      const percentDifference = originalValue !== 0 ? (difference / originalValue) * 100 : 0;
      const isSignificant = Math.abs(difference) > param.threshold;
      
      comparisons.push({
        parameter: param.name,
        original: originalValue,
        calculated: calculatedValue,
        difference: Math.round(difference * 100) / 100,
        percentDifference: Math.round(percentDifference * 10) / 10,
        isSignificant
      });
    });
    
    return comparisons;
  }
}
