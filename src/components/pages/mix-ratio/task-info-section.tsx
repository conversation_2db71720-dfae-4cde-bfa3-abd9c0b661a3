'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

import type { MixRatio } from '@/types/mixRatio';

interface TaskInfoSectionProps {
  mixRatio: MixRatio;
  onUpdate: (mixRatio: MixRatio) => void;
}

export function TaskInfoSection({ mixRatio, onUpdate }: TaskInfoSectionProps) {
  const handleFieldChange = (field: keyof MixRatio, value: any) => {
    onUpdate({
      ...mixRatio,
      [field]: value
    });
  };

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-blue-700">
          任务信息
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-4 gap-3 text-sm">
          {/* 第一行 */}
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">任务编号</Label>
            <Input
              value={mixRatio.taskNumber}
              onChange={(e) => handleFieldChange('taskNumber', e.target.value)}
              className="h-7 text-xs"
              readOnly
            />
          </div>
          
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">强度</Label>
            <Input
              value={mixRatio.strength}
              onChange={(e) => handleFieldChange('strength', e.target.value)}
              className="h-7 text-xs"
            />
          </div>

          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">抗渗</Label>
            <Input
              value={mixRatio.impermeability}
              onChange={(e) => handleFieldChange('impermeability', e.target.value)}
              className="h-7 text-xs"
              placeholder="P6"
            />
          </div>

          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">抗冻</Label>
            <Input
              value={mixRatio.freezeResistance}
              onChange={(e) => handleFieldChange('freezeResistance', e.target.value)}
              className="h-7 text-xs"
              placeholder="F100"
            />
          </div>

          {/* 第二行 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">配比编号</Label>
            <Input
              value={mixRatio.code}
              onChange={(e) => handleFieldChange('code', e.target.value)}
              className="h-8"
            />
          </div>
          
          <div className="space-y-2">
            <Label className="text-sm font-medium">工程名称</Label>
            <Input
              value={mixRatio.projectName}
              onChange={(e) => handleFieldChange('projectName', e.target.value)}
              className="h-8"
              readOnly
            />
          </div>
          
          <div className="space-y-2">
            <Label className="text-sm font-medium">施工部位</Label>
            <Input
              value={mixRatio.constructionSite}
              onChange={(e) => handleFieldChange('constructionSite', e.target.value)}
              className="h-8"
              readOnly
            />
          </div>
          
          <div className="space-y-2">
            <Label className="text-sm font-medium">坍落度</Label>
            <div className="flex items-center space-x-1">
              <Input
                type="number"
                value={mixRatio.slump}
                onChange={(e) => handleFieldChange('slump', Number(e.target.value))}
                className="h-8"
                min="0"
                max="300"
              />
              <span className="text-xs text-muted-foreground">mm</span>
            </div>
          </div>

          {/* 第三行 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">浇筑方式</Label>
            <Input
              value={mixRatio.pouringMethod}
              onChange={(e) => handleFieldChange('pouringMethod', e.target.value)}
              className="h-8"
              readOnly
            />
          </div>
          
          <div className="col-span-3 flex items-end">
            <div className="text-sm text-muted-foreground">
              状态: 
              <Badge 
                variant={
                  mixRatio.status === 'draft' ? 'secondary' :
                  mixRatio.status === 'approved' ? 'default' : 'destructive'
                }
                className="ml-2"
              >
                {mixRatio.status === 'draft' ? '草稿' :
                 mixRatio.status === 'approved' ? '已审核' : '已发送'}
              </Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
