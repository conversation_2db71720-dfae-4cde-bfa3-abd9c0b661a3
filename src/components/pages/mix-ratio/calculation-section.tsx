'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calculator, FileText, RotateCcw, CheckCircle } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { ReverseCalculationForm } from './reverse-calculation-form';

import type { MixRatio, Material, CalculationMethod } from '@/types/mixRatio';

interface CalculationSectionProps {
  mixRatio: MixRatio;
  onUpdate: (mixRatio: MixRatio) => void;
  materials: Material[];
}

export function CalculationSection({ mixRatio, onUpdate, materials }: CalculationSectionProps) {
  const [isCalculating, setIsCalculating] = useState(false);
  const [isReverseFormOpen, setIsReverseFormOpen] = useState(false);

  const handleParamChange = (field: keyof typeof mixRatio.calculationParams, value: number) => {
    onUpdate({
      ...mixRatio,
      calculationParams: {
        ...mixRatio.calculationParams,
        [field]: value
      }
    });
  };

  const handleMethodChange = (method: CalculationMethod) => {
    onUpdate({
      ...mixRatio,
      calculationMethod: method
    });
  };

  // 反算功能 - 根据配比计算书算法
  const handleReverseCalculation = async (params: any) => {
    setIsCalculating(true);

    try {
      // 模拟计算过程
      await new Promise(resolve => setTimeout(resolve, 1000));

      const { density, waterCementRatio, waterContent, sandRatio } = params;
      
      // 基础计算
      const totalWeight = density * 1000; // kg/m³
      const cementContent = waterContent / waterCementRatio; // 水泥用量
      const aggregateContent = totalWeight - waterContent - cementContent; // 骨料总量
      const sandContent = aggregateContent * (sandRatio / 100); // 砂用量
      const stoneContent = aggregateContent - sandContent; // 石子用量
      
      // 外加剂计算
      const additiveContent = cementContent * (params.additiveRatio / 100);
      
      // 更新配比项目
      const updatedItems = mixRatio.items.map(item => {
        let theoreticalAmount = 0;
        
        switch (item.materialName) {
          case '水泥':
            theoreticalAmount = Math.round(cementContent);
            break;
          case '水':
            theoreticalAmount = Math.round(waterContent);
            break;
          case '沙子':
            theoreticalAmount = Math.round(sandContent);
            break;
          case '石子':
            theoreticalAmount = Math.round(stoneContent);
            break;
          case '外加剂':
            theoreticalAmount = Math.round(additiveContent);
            break;
          default:
            theoreticalAmount = item.theoreticalAmount;
        }
        
        return {
          ...item,
          theoreticalAmount,
          actualAmount: theoreticalAmount,
          designValue: theoreticalAmount
        };
      });
      
      // 更新计算参数
      const updatedParams = {
        ...mixRatio.calculationParams,
        ...params,
        totalWeight: Math.round(totalWeight),
        mixingTime: Math.round(60 + (totalWeight - 2000) / 50) // 简单的搅拌时间计算
      };
      
      onUpdate({
        ...mixRatio,
        calculationParams: updatedParams,
        items: updatedItems
      });
      
    } catch (error) {
      console.error('计算失败:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleApplyCalculation = () => {
    // 应用计算结果到配比表格
    console.log('应用计算结果');
  };

  const calculationMethods = [
    { value: 'no_flyash_excess', label: '不考虑粉煤灰超量系数' },
    { value: 'consider_flyash', label: '考虑粉煤灰系数' },
    { value: 'flyash_volume_conversion', label: '考虑粉煤灰系数且体积折算' },
    { value: 'excess_flyash', label: '超量煤灰' }
  ];

  return (
    <>
      <Card className="border-l-4 border-l-green-500">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-green-700">
            计算参数
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0 space-y-3">
          {/* 基础参数 */}
          <div className="grid grid-cols-6 gap-2 text-xs">
            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">密度</Label>
              <div className="flex items-center space-x-1">
                <Input
                  type="number"
                  step="0.01"
                  value={mixRatio.calculationParams.density}
                  onChange={(e) => handleParamChange('density', Number(e.target.value))}
                  className="h-6 text-xs"
                />
                <span className="text-xs text-muted-foreground">t/m³</span>
              </div>
            </div>

            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">总重量</Label>
              <div className="flex items-center space-x-1">
                <Input
                  type="number"
                  value={mixRatio.calculationParams.totalWeight}
                  className="h-6 text-xs"
                  readOnly
                />
                <span className="text-xs text-muted-foreground">kg</span>
              </div>
            </div>

            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">搅拌时间</Label>
              <div className="flex items-center space-x-1">
                <Input
                  type="number"
                  value={mixRatio.calculationParams.mixingTime}
                  onChange={(e) => handleParamChange('mixingTime', Number(e.target.value))}
                  className="h-6 text-xs"
                />
                <span className="text-xs text-muted-foreground">秒</span>
              </div>
            </div>

            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">水胶比</Label>
              <Input
                type="number"
                step="0.01"
                value={mixRatio.calculationParams.waterCementRatio}
                onChange={(e) => handleParamChange('waterCementRatio', Number(e.target.value))}
                className="h-6 text-xs"
              />
            </div>

            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">用水量</Label>
              <Input
                type="number"
                value={mixRatio.calculationParams.waterContent}
                onChange={(e) => handleParamChange('waterContent', Number(e.target.value))}
                className="h-6 text-xs"
              />
            </div>

            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">沙率%</Label>
              <Input
                type="number"
                value={mixRatio.calculationParams.sandRatio}
                onChange={(e) => handleParamChange('sandRatio', Number(e.target.value))}
                className="h-6 text-xs"
              />
            </div>
          </div>

          {/* 计算方法选择 */}
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">计算方法</Label>
            <Select value={mixRatio.calculationMethod} onValueChange={handleMethodChange}>
              <SelectTrigger className="h-6 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {calculationMethods.map((method) => (
                  <SelectItem key={method.value} value={method.value}>
                    {method.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center justify-end space-x-2">
            <Button
              size="sm"
              onClick={() => setIsReverseFormOpen(true)}
              disabled={isCalculating}
            >
              <Calculator className="mr-2 h-3 w-3" />
              {isCalculating ? '计算中...' : '反算'}
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={handleApplyCalculation}
            >
              <CheckCircle className="mr-2 h-3 w-3" />
              应用
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 反算参数表单 */}
      <ReverseCalculationForm
        isOpen={isReverseFormOpen}
        onClose={() => setIsReverseFormOpen(false)}
        onCalculate={handleReverseCalculation}
        currentParams={mixRatio.calculationParams}
      />
    </>
  );
}
