'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calculator, 
  CheckCircle, 
  AlertTriangle, 
  TrendingUp, 
  DollarSign,
  Target,
  Zap,
  Brain
} from 'lucide-react';

interface CalculationParams {
  density: number;
  waterRatio: number;
  waterAmount: number;
  sandRatio: number;
  additiveRatio: number;
  flyashRatio: number;
  antifreezeRatio: number;
  totalWeight: number;
  mixingTime: number;
  targetStrength: number;
  slump: number;
  temperature: number;
}

interface MaterialItem {
  id: string;
  name: string;
  type: 'aggregate' | 'powder' | 'water' | 'additive';
  amount: number;
  waterContent: number;
  actualAmount: number;
  siloName: string;
  price?: number;
  density?: number;
}

interface CalculationResult {
  materials: MaterialItem[];
  totalCost: number;
  strengthPrediction: number;
  qualityScore: number;
  warnings: string[];
  suggestions: string[];
  carbonFootprint: number;
}

interface RatioCalculationEngineProps {
  params: CalculationParams;
  materials: MaterialItem[];
  onResultChange: (result: CalculationResult) => void;
}

export function RatioCalculationEngine({ params, materials, onResultChange }: RatioCalculationEngineProps) {
  const [isCalculating, setIsCalculating] = useState(false);
  const [result, setResult] = useState<CalculationResult | null>(null);

  // 高级配比计算算法
  const calculateAdvancedRatio = async (): Promise<CalculationResult> => {
    const { 
      density, 
      waterRatio, 
      waterAmount, 
      sandRatio, 
      additiveRatio, 
      flyashRatio,
      antifreezeRatio,
      targetStrength,
      slump,
      temperature 
    } = params;

    // 基础计算
    const totalWeight = density * 1000;
    const cementAmount = waterAmount / waterRatio * (1 - flyashRatio / 100);
    const flyashAmount = waterAmount / waterRatio * (flyashRatio / 100);
    const aggregateTotal = totalWeight - waterAmount - cementAmount - flyashAmount;
    const sandAmount = aggregateTotal * (sandRatio / 100);
    const stoneAmount = aggregateTotal - sandAmount;
    const additiveAmount = (cementAmount + flyashAmount) * (additiveRatio / 100);
    const antifreezeAmount = (cementAmount + flyashAmount) * (antifreezeRatio / 100);

    // 温度修正
    const tempCorrection = temperature < 5 ? 1.1 : temperature > 30 ? 0.95 : 1.0;
    const correctedWaterAmount = waterAmount * tempCorrection;

    // 坍落度修正
    const slumpCorrection = slump > 180 ? 1.05 : slump < 120 ? 0.98 : 1.0;
    const finalAdditiveAmount = additiveAmount * slumpCorrection;

    // 更新材料用量
    const updatedMaterials: MaterialItem[] = materials.map(item => {
      switch (item.type) {
        case 'powder':
          if (item.name.includes('水泥')) {
            return { 
              ...item, 
              amount: Math.round(cementAmount), 
              actualAmount: Math.round(cementAmount),
              price: item.price || 420
            };
          } else if (item.name.includes('粉煤灰')) {
            return { 
              ...item, 
              amount: Math.round(flyashAmount), 
              actualAmount: Math.round(flyashAmount),
              price: item.price || 180
            };
          }
          return item;
        case 'water':
          return { 
            ...item, 
            amount: Math.round(correctedWaterAmount), 
            actualAmount: Math.round(correctedWaterAmount),
            price: item.price || 3
          };
        case 'aggregate':
          if (item.name.includes('砂')) {
            const actualSand = sandAmount * (1 - item.waterContent / 100);
            return { 
              ...item, 
              amount: Math.round(sandAmount), 
              actualAmount: Math.round(actualSand),
              price: item.price || 85
            };
          } else {
            const actualStone = stoneAmount * (1 - item.waterContent / 100);
            return { 
              ...item, 
              amount: Math.round(stoneAmount), 
              actualAmount: Math.round(actualStone),
              price: item.price || 75
            };
          }
        case 'additive':
          if (item.name.includes('防冻')) {
            return { 
              ...item, 
              amount: Math.round(antifreezeAmount * 10) / 10, 
              actualAmount: Math.round(antifreezeAmount * 10) / 10,
              price: item.price || 3200
            };
          } else {
            return { 
              ...item, 
              amount: Math.round(finalAdditiveAmount * 10) / 10, 
              actualAmount: Math.round(finalAdditiveAmount * 10) / 10,
              price: item.price || 2800
            };
          }
        default:
          return item;
      }
    });

    // 成本计算
    const totalCost = updatedMaterials.reduce((sum, material) => {
      const price = material.price || 0;
      const amount = material.amount / 1000; // 转换为吨
      return sum + (price * amount);
    }, 0);

    // 强度预测 (简化的Abrams定律)
    const effectiveWaterCementRatio = correctedWaterAmount / (cementAmount + flyashAmount * 0.7);
    const strengthPrediction = Math.round(
      (cementAmount * 0.8 + flyashAmount * 0.5) / effectiveWaterCementRatio * 0.15
    );

    // 质量评分
    let qualityScore = 100;
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // 质量检查
    if (effectiveWaterCementRatio > 0.6) {
      qualityScore -= 20;
      warnings.push('水胶比过高，可能影响强度和耐久性');
      suggestions.push('建议降低水胶比或增加胶凝材料用量');
    }

    if (sandRatio < 30 || sandRatio > 40) {
      qualityScore -= 10;
      warnings.push('砂率不在最佳范围内');
      suggestions.push('建议将砂率调整到30-40%之间');
    }

    if (additiveRatio < 0.8 || additiveRatio > 2.5) {
      qualityScore -= 15;
      warnings.push('外加剂掺量可能不合适');
      suggestions.push('建议调整外加剂掺量到0.8-2.5%之间');
    }

    if (Math.abs(strengthPrediction - targetStrength) > targetStrength * 0.1) {
      qualityScore -= 25;
      warnings.push('预测强度与目标强度偏差较大');
      suggestions.push('建议调整胶凝材料用量或水胶比');
    }

    // 经济性建议
    if (totalCost > 200) {
      suggestions.push('成本较高，可考虑增加粉煤灰掺量降低成本');
    }

    if (flyashRatio > 0) {
      suggestions.push('使用粉煤灰有助于提高工作性和降低成本');
    }

    // 碳足迹计算 (简化)
    const carbonFootprint = Math.round(
      (cementAmount * 0.85 + flyashAmount * 0.1) * 0.001 * 100
    ) / 100;

    return {
      materials: updatedMaterials,
      totalCost: Math.round(totalCost * 100) / 100,
      strengthPrediction,
      qualityScore: Math.max(0, qualityScore),
      warnings,
      suggestions,
      carbonFootprint
    };
  };

  const handleCalculate = async () => {
    setIsCalculating(true);
    try {
      // 模拟计算延迟
      await new Promise(resolve => setTimeout(resolve, 1500));
      const calculationResult = await calculateAdvancedRatio();
      setResult(calculationResult);
      onResultChange(calculationResult);
    } catch (error) {
      console.error('计算错误:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const getQualityColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getQualityLabel = (score: number) => {
    if (score >= 90) return '优秀';
    if (score >= 70) return '良好';
    if (score >= 50) return '一般';
    return '需改进';
  };

  return (
    <div className="space-y-4">
      {/* 计算控制 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <Brain className="h-5 w-5" />
            智能配比计算引擎
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              基于先进算法进行配比优化计算，考虑温度、坍落度等因素
            </div>
            <Button 
              onClick={handleCalculate} 
              disabled={isCalculating}
              className="flex items-center gap-2"
            >
              {isCalculating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  计算中...
                </>
              ) : (
                <>
                  <Calculator className="h-4 w-4" />
                  开始计算
                </>
              )}
            </Button>
          </div>
          
          {isCalculating && (
            <div className="mt-4">
              <div className="text-sm text-muted-foreground mb-2">计算进度</div>
              <Progress value={66} className="h-2" />
              <div className="text-xs text-muted-foreground mt-1">
                正在进行强度预测和质量评估...
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 计算结果 */}
      {result && (
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="quality">质量分析</TabsTrigger>
            <TabsTrigger value="cost">成本分析</TabsTrigger>
            <TabsTrigger value="suggestions">优化建议</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {result.strengthPrediction}
                  </div>
                  <div className="text-sm text-muted-foreground">预测强度 (MPa)</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <div className={`text-2xl font-bold ${getQualityColor(result.qualityScore)}`}>
                    {result.qualityScore}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    质量评分 ({getQualityLabel(result.qualityScore)})
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">
                    ¥{result.totalCost}
                  </div>
                  <div className="text-sm text-muted-foreground">总成本 (元/m³)</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {result.carbonFootprint}
                  </div>
                  <div className="text-sm text-muted-foreground">碳足迹 (kg CO₂)</div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="quality" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  质量分析报告
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>综合质量评分</span>
                  <div className="flex items-center gap-2">
                    <Progress value={result.qualityScore} className="w-32 h-2" />
                    <span className={`font-bold ${getQualityColor(result.qualityScore)}`}>
                      {result.qualityScore}/100
                    </span>
                  </div>
                </div>
                
                {result.warnings.length > 0 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-1">
                        <div className="font-medium">质量警告：</div>
                        {result.warnings.map((warning, index) => (
                          <div key={index} className="text-sm">• {warning}</div>
                        ))}
                      </div>
                    </AlertDescription>
                  </Alert>
                )}
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <Label>强度预测准确度</Label>
                    <div className="font-medium">
                      {Math.abs(result.strengthPrediction - params.targetStrength) <= params.targetStrength * 0.05 ? '高' : '中'}
                    </div>
                  </div>
                  <div>
                    <Label>配比合理性</Label>
                    <div className="font-medium">
                      {result.qualityScore >= 80 ? '优秀' : result.qualityScore >= 60 ? '良好' : '需改进'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="cost" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  成本分析
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-lg font-bold">
                    总成本: ¥{result.totalCost}/m³
                  </div>
                  
                  <div className="space-y-2">
                    {result.materials.map((material) => {
                      const cost = (material.price || 0) * (material.amount / 1000);
                      const percentage = (cost / result.totalCost) * 100;
                      return (
                        <div key={material.id} className="flex items-center justify-between text-sm">
                          <span>{material.name}</span>
                          <div className="flex items-center gap-2">
                            <Progress value={percentage} className="w-20 h-2" />
                            <span className="w-16 text-right">¥{cost.toFixed(2)}</span>
                            <span className="w-12 text-right text-muted-foreground">
                              {percentage.toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="suggestions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  优化建议
                </CardTitle>
              </CardHeader>
              <CardContent>
                {result.suggestions.length > 0 ? (
                  <div className="space-y-2">
                    {result.suggestions.map((suggestion, index) => (
                      <Alert key={index}>
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>{suggestion}</AlertDescription>
                      </Alert>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                    <div>配比已优化，无需调整</div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
