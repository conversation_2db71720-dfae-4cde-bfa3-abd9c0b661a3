'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
// import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  History, 
  Settings, 
  RefreshCw, 
  Printer,
  Database,
  Users,
  Calculator
} from 'lucide-react';

import type { MixingStation } from '@/types/mixRatio';

interface MixRatioToolbarProps {
  unifiedRatio: boolean;
  onUnifiedRatioChange: (checked: boolean) => void;
  selectedStation: string;
  onStationChange: (stationId: string) => void;
  stations: MixingStation[];
  selectedRatioCode: string;
  onRatioCodeChange: (code: string) => void;
  onOpenHistory: () => void;
  onOpenMaterialManagement: () => void;
  onOpenSiloManagement: () => void;
  onOpenQualityStandard: () => void;
  onOpenParameterSettings: () => void;
  onRefreshSilos: () => void;
}

export function MixRatioToolbar({
  unifiedRatio,
  onUnifiedRatioChange,
  selectedStation,
  onStationChange,
  stations,
  selectedRatioCode,
  onRatioCodeChange,
  onOpenHistory,
  onOpenMaterialManagement,
  onOpenSiloManagement,
  onOpenQualityStandard,
  onOpenParameterSettings,
  onRefreshSilos,
}: MixRatioToolbarProps) {
  
  // 模拟配比编号列表
  const ratioCodes = [
    'C125-00003',
    'C125-00004', 
    'C125-00005',
    'C130-00001',
    'C130-00002'
  ];

  return (
    <div className="border-b bg-muted/30 p-2">
      <div className="flex items-center justify-between">
          {/* 左侧控件 */}
          <div className="flex items-center space-x-3">
          {/* 所有站统一配比 */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="unified-ratio"
              checked={unifiedRatio}
              onCheckedChange={onUnifiedRatioChange}
            />
            <Label htmlFor="unified-ratio" className="text-xs font-medium">
              统一配比
            </Label>
          </div>

          {/* 搅拌站选择 */}
          <div className="flex items-center space-x-1">
            <Label className="text-xs">站点:</Label>
            <Select value={selectedStation} onValueChange={onStationChange}>
              <SelectTrigger className="w-24 h-7">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {stations.map((station) => (
                  <SelectItem key={station.id} value={station.id}>
                    {station.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 配比编号选择 */}
          <div className="flex items-center space-x-1">
            <Label className="text-xs">配比:</Label>
            <Select value={selectedRatioCode} onValueChange={onRatioCodeChange}>
              <SelectTrigger className="w-28 h-7">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {ratioCodes.map((code) => (
                  <SelectItem key={code} value={code}>
                    {code}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 右侧控件 */}
        <div className="flex items-center space-x-2">
          {/* 料仓管理 */}
          <Button variant="outline" size="sm" onClick={onOpenSiloManagement} title="料仓管理">
            <Database className="h-3 w-3" />
          </Button>

          {/* 材料管理 */}
          <Button variant="outline" size="sm" onClick={onOpenMaterialManagement} title="材料管理">
            <Users className="h-3 w-3" />
          </Button>

          {/* 历史记录 */}
          <Button variant="outline" size="sm" onClick={onOpenHistory} title="历史记录">
            <History className="h-3 w-3" />
          </Button>

          {/* 设置 */}
          <Button variant="outline" size="sm" onClick={onOpenParameterSettings} title="参数设置">
            <Settings className="h-3 w-3" />
          </Button>

          {/* 打印 */}
          <Button variant="outline" size="sm" title="打印配比">
            <Printer className="h-3 w-3" />
          </Button>

          {/* 刷新 */}
          <Button variant="outline" size="sm" onClick={onRefreshSilos} title="刷新料仓">
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
}
