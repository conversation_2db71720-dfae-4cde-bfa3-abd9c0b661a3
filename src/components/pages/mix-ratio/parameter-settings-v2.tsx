'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Save, 
  RotateCcw, 
  AlertTriangle, 
  CheckCircle, 
  Info,
  Database,
  Calculator,
  Printer,
  Shield
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface SystemSettings {
  // 计算参数
  calculation: {
    defaultDensity: number;
    defaultWaterRatio: number;
    defaultSandRatio: number;
    defaultAdditiveRatio: number;
    mixingTimeBase: number;
    mixingTimeIncrement: number;
    toleranceRange: number;
  };
  
  // 质量控制
  quality: {
    enableQualityCheck: boolean;
    strengthTolerance: number;
    slumpTolerance: number;
    densityTolerance: number;
    autoReject: boolean;
    requireApproval: boolean;
  };
  
  // 料仓管理
  silo: {
    autoRefreshInterval: number;
    lowLevelWarning: number;
    criticalLevelWarning: number;
    enableAlerts: boolean;
    distributionMethod: 'average' | 'capacity';
  };
  
  // 系统配置
  system: {
    autoSave: boolean;
    autoSaveInterval: number;
    backupEnabled: boolean;
    logLevel: 'error' | 'warning' | 'info' | 'debug';
    sessionTimeout: number;
  };
  
  // 打印设置
  print: {
    defaultPrinter: string;
    paperSize: 'A4' | 'A5' | 'Letter';
    includeQRCode: boolean;
    includeBarcode: boolean;
    copies: number;
  };
}

interface ParameterSettingsProps {
  onClose: () => void;
  onSettingsChange?: (settings: SystemSettings) => void;
}

export function ParameterSettingsV2({ onClose, onSettingsChange }: ParameterSettingsProps) {
  const [settings, setSettings] = useState<SystemSettings>({
    calculation: {
      defaultDensity: 2.36,
      defaultWaterRatio: 0.45,
      defaultSandRatio: 35,
      defaultAdditiveRatio: 1.3,
      mixingTimeBase: 60,
      mixingTimeIncrement: 50,
      toleranceRange: 5
    },
    quality: {
      enableQualityCheck: true,
      strengthTolerance: 5,
      slumpTolerance: 20,
      densityTolerance: 0.05,
      autoReject: false,
      requireApproval: true
    },
    silo: {
      autoRefreshInterval: 30,
      lowLevelWarning: 20,
      criticalLevelWarning: 10,
      enableAlerts: true,
      distributionMethod: 'capacity'
    },
    system: {
      autoSave: true,
      autoSaveInterval: 300,
      backupEnabled: true,
      logLevel: 'info',
      sessionTimeout: 3600
    },
    print: {
      defaultPrinter: 'Default',
      paperSize: 'A4',
      includeQRCode: true,
      includeBarcode: false,
      copies: 1
    }
  });

  const [hasChanges, setHasChanges] = useState(false);
  const { toast } = useToast();

  const updateSettings = (section: keyof SystemSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    onSettingsChange?.(settings);
    setHasChanges(false);
    toast({
      title: '保存成功',
      description: '系统参数已更新'
    });
  };

  const handleReset = () => {
    // 重置为默认值
    setSettings({
      calculation: {
        defaultDensity: 2.36,
        defaultWaterRatio: 0.45,
        defaultSandRatio: 35,
        defaultAdditiveRatio: 1.3,
        mixingTimeBase: 60,
        mixingTimeIncrement: 50,
        toleranceRange: 5
      },
      quality: {
        enableQualityCheck: true,
        strengthTolerance: 5,
        slumpTolerance: 20,
        densityTolerance: 0.05,
        autoReject: false,
        requireApproval: true
      },
      silo: {
        autoRefreshInterval: 30,
        lowLevelWarning: 20,
        criticalLevelWarning: 10,
        enableAlerts: true,
        distributionMethod: 'capacity'
      },
      system: {
        autoSave: true,
        autoSaveInterval: 300,
        backupEnabled: true,
        logLevel: 'info',
        sessionTimeout: 3600
      },
      print: {
        defaultPrinter: 'Default',
        paperSize: 'A4',
        includeQRCode: true,
        includeBarcode: false,
        copies: 1
      }
    });
    setHasChanges(true);
    toast({
      title: '重置成功',
      description: '参数已重置为默认值'
    });
  };

  return (
    <div className="space-y-4">
      {/* 保存提示 */}
      {hasChanges && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">您有未保存的更改</span>
              </div>
              <div className="flex space-x-2">
                <Button size="sm" variant="outline" onClick={handleReset}>
                  <RotateCcw className="mr-2 h-3 w-3" />
                  重置
                </Button>
                <Button size="sm" onClick={handleSave}>
                  <Save className="mr-2 h-3 w-3" />
                  保存
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="calculation" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="calculation">计算参数</TabsTrigger>
          <TabsTrigger value="quality">质量控制</TabsTrigger>
          <TabsTrigger value="silo">料仓管理</TabsTrigger>
          <TabsTrigger value="system">系统配置</TabsTrigger>
          <TabsTrigger value="print">打印设置</TabsTrigger>
        </TabsList>

        {/* 计算参数 */}
        <TabsContent value="calculation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                默认计算参数
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>默认密度 (t/m³)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={settings.calculation.defaultDensity}
                    onChange={(e) => updateSettings('calculation', 'defaultDensity', Number(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>默认水胶比</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={settings.calculation.defaultWaterRatio}
                    onChange={(e) => updateSettings('calculation', 'defaultWaterRatio', Number(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>默认砂率 (%)</Label>
                  <Input
                    type="number"
                    value={settings.calculation.defaultSandRatio}
                    onChange={(e) => updateSettings('calculation', 'defaultSandRatio', Number(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>默认外加剂比例 (%)</Label>
                  <Input
                    type="number"
                    step="0.1"
                    value={settings.calculation.defaultAdditiveRatio}
                    onChange={(e) => updateSettings('calculation', 'defaultAdditiveRatio', Number(e.target.value))}
                  />
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>搅拌时间基准值 (秒)</Label>
                  <div className="flex items-center space-x-4">
                    <Slider
                      value={[settings.calculation.mixingTimeBase]}
                      onValueChange={(value) => updateSettings('calculation', 'mixingTimeBase', value[0])}
                      max={120}
                      min={30}
                      step={5}
                      className="flex-1"
                    />
                    <span className="w-16 text-sm">{settings.calculation.mixingTimeBase}秒</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>搅拌时间增量系数</Label>
                  <div className="flex items-center space-x-4">
                    <Slider
                      value={[settings.calculation.mixingTimeIncrement]}
                      onValueChange={(value) => updateSettings('calculation', 'mixingTimeIncrement', value[0])}
                      max={100}
                      min={10}
                      step={5}
                      className="flex-1"
                    />
                    <span className="w-16 text-sm">{settings.calculation.mixingTimeIncrement}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>容差范围 (%)</Label>
                  <div className="flex items-center space-x-4">
                    <Slider
                      value={[settings.calculation.toleranceRange]}
                      onValueChange={(value) => updateSettings('calculation', 'toleranceRange', value[0])}
                      max={20}
                      min={1}
                      step={1}
                      className="flex-1"
                    />
                    <span className="w-16 text-sm">±{settings.calculation.toleranceRange}%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 质量控制 */}
        <TabsContent value="quality" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                质量控制参数
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用质量检查</Label>
                  <div className="text-sm text-muted-foreground">
                    自动检查配比参数是否符合质量标准
                  </div>
                </div>
                <Switch
                  checked={settings.quality.enableQualityCheck}
                  onCheckedChange={(checked) => updateSettings('quality', 'enableQualityCheck', checked)}
                />
              </div>
              
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>强度容差 (%)</Label>
                  <Input
                    type="number"
                    value={settings.quality.strengthTolerance}
                    onChange={(e) => updateSettings('quality', 'strengthTolerance', Number(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>坍落度容差 (mm)</Label>
                  <Input
                    type="number"
                    value={settings.quality.slumpTolerance}
                    onChange={(e) => updateSettings('quality', 'slumpTolerance', Number(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>密度容差 (t/m³)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={settings.quality.densityTolerance}
                    onChange={(e) => updateSettings('quality', 'densityTolerance', Number(e.target.value))}
                  />
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>自动拒绝不合格配比</Label>
                    <div className="text-sm text-muted-foreground">
                      超出容差范围的配比将被自动拒绝
                    </div>
                  </div>
                  <Switch
                    checked={settings.quality.autoReject}
                    onCheckedChange={(checked) => updateSettings('quality', 'autoReject', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>需要审核批准</Label>
                    <div className="text-sm text-muted-foreground">
                      配比发送前需要审核人员批准
                    </div>
                  </div>
                  <Switch
                    checked={settings.quality.requireApproval}
                    onCheckedChange={(checked) => updateSettings('quality', 'requireApproval', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 料仓管理 */}
        <TabsContent value="silo" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                料仓管理参数
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>自动刷新间隔 (秒)</Label>
                  <Select
                    value={settings.silo.autoRefreshInterval.toString()}
                    onValueChange={(value) => updateSettings('silo', 'autoRefreshInterval', Number(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10秒</SelectItem>
                      <SelectItem value="30">30秒</SelectItem>
                      <SelectItem value="60">1分钟</SelectItem>
                      <SelectItem value="300">5分钟</SelectItem>
                      <SelectItem value="600">10分钟</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>物料分配方式</Label>
                  <Select
                    value={settings.silo.distributionMethod}
                    onValueChange={(value: any) => updateSettings('silo', 'distributionMethod', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="average">平均分配</SelectItem>
                      <SelectItem value="capacity">按容量分配</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>低位报警阈值 (%)</Label>
                  <div className="flex items-center space-x-4">
                    <Slider
                      value={[settings.silo.lowLevelWarning]}
                      onValueChange={(value) => updateSettings('silo', 'lowLevelWarning', value[0])}
                      max={50}
                      min={5}
                      step={5}
                      className="flex-1"
                    />
                    <span className="w-16 text-sm">{settings.silo.lowLevelWarning}%</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>严重报警阈值 (%)</Label>
                  <div className="flex items-center space-x-4">
                    <Slider
                      value={[settings.silo.criticalLevelWarning]}
                      onValueChange={(value) => updateSettings('silo', 'criticalLevelWarning', value[0])}
                      max={30}
                      min={1}
                      step={1}
                      className="flex-1"
                    />
                    <span className="w-16 text-sm">{settings.silo.criticalLevelWarning}%</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用报警提醒</Label>
                    <div className="text-sm text-muted-foreground">
                      料仓存量不足时发送报警通知
                    </div>
                  </div>
                  <Switch
                    checked={settings.silo.enableAlerts}
                    onCheckedChange={(checked) => updateSettings('silo', 'enableAlerts', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 系统配置 */}
        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                系统配置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>自动保存</Label>
                    <div className="text-sm text-muted-foreground">
                      定期自动保存配比数据
                    </div>
                  </div>
                  <Switch
                    checked={settings.system.autoSave}
                    onCheckedChange={(checked) => updateSettings('system', 'autoSave', checked)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>自动保存间隔 (秒)</Label>
                  <Select
                    value={settings.system.autoSaveInterval.toString()}
                    onValueChange={(value) => updateSettings('system', 'autoSaveInterval', Number(value))}
                    disabled={!settings.system.autoSave}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="60">1分钟</SelectItem>
                      <SelectItem value="300">5分钟</SelectItem>
                      <SelectItem value="600">10分钟</SelectItem>
                      <SelectItem value="1800">30分钟</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用数据备份</Label>
                    <div className="text-sm text-muted-foreground">
                      定期备份重要数据
                    </div>
                  </div>
                  <Switch
                    checked={settings.system.backupEnabled}
                    onCheckedChange={(checked) => updateSettings('system', 'backupEnabled', checked)}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>日志级别</Label>
                    <Select
                      value={settings.system.logLevel}
                      onValueChange={(value: any) => updateSettings('system', 'logLevel', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="error">错误</SelectItem>
                        <SelectItem value="warning">警告</SelectItem>
                        <SelectItem value="info">信息</SelectItem>
                        <SelectItem value="debug">调试</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>会话超时 (秒)</Label>
                    <Select
                      value={settings.system.sessionTimeout.toString()}
                      onValueChange={(value) => updateSettings('system', 'sessionTimeout', Number(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1800">30分钟</SelectItem>
                        <SelectItem value="3600">1小时</SelectItem>
                        <SelectItem value="7200">2小时</SelectItem>
                        <SelectItem value="14400">4小时</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 打印设置 */}
        <TabsContent value="print" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Printer className="h-5 w-5" />
                打印设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>默认打印机</Label>
                  <Select
                    value={settings.print.defaultPrinter}
                    onValueChange={(value) => updateSettings('print', 'defaultPrinter', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Default">默认打印机</SelectItem>
                      <SelectItem value="HP LaserJet">HP LaserJet</SelectItem>
                      <SelectItem value="Canon PIXMA">Canon PIXMA</SelectItem>
                      <SelectItem value="Epson L3150">Epson L3150</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>纸张尺寸</Label>
                  <Select
                    value={settings.print.paperSize}
                    onValueChange={(value: any) => updateSettings('print', 'paperSize', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="A4">A4</SelectItem>
                      <SelectItem value="A5">A5</SelectItem>
                      <SelectItem value="Letter">Letter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>包含二维码</Label>
                    <div className="text-sm text-muted-foreground">
                      在打印文档中包含二维码
                    </div>
                  </div>
                  <Switch
                    checked={settings.print.includeQRCode}
                    onCheckedChange={(checked) => updateSettings('print', 'includeQRCode', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>包含条形码</Label>
                    <div className="text-sm text-muted-foreground">
                      在打印文档中包含条形码
                    </div>
                  </div>
                  <Switch
                    checked={settings.print.includeBarcode}
                    onCheckedChange={(checked) => updateSettings('print', 'includeBarcode', checked)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>打印份数</Label>
                  <div className="flex items-center space-x-4">
                    <Slider
                      value={[settings.print.copies]}
                      onValueChange={(value) => updateSettings('print', 'copies', value[0])}
                      max={10}
                      min={1}
                      step={1}
                      className="flex-1"
                    />
                    <span className="w-16 text-sm">{settings.print.copies} 份</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 底部操作栏 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Info className="h-4 w-4" />
              <span>修改参数后请及时保存，以免丢失更改</span>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleReset}>
                <RotateCcw className="mr-2 h-4 w-4" />
                重置为默认
              </Button>
              <Button onClick={handleSave} disabled={!hasChanges}>
                <Save className="mr-2 h-4 w-4" />
                保存设置
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
