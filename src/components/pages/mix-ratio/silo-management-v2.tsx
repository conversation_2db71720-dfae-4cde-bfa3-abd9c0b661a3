'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Plus, Trash2, Edit, Save, X, RefreshCw, AlertTriangle, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface SiloInfo {
  id: string;
  identifier: string;
  name: string;
  type: 'aggregate' | 'powder' | 'water' | 'additive';
  capacity: number;
  current: number;
  minLevel: number;
  maxLevel: number;
  isEnabled: boolean;
  stationId: string;
  displayOrder: number;
  materialCode?: string;
  lastUpdated: string;
  status: 'normal' | 'warning' | 'critical' | 'offline';
}

interface SiloManagementProps {
  onClose: () => void;
  onSilosChange?: (silos: SiloInfo[]) => void;
  currentStation: string;
}

export function SiloManagementV2({ onClose, onSilosChange, currentStation }: SiloManagementProps) {
  const [silos, setSilos] = useState<SiloInfo[]>([
    {
      id: '1',
      identifier: '粉料1',
      name: '1#水泥罐',
      type: 'powder',
      capacity: 100,
      current: 85,
      minLevel: 10,
      maxLevel: 95,
      isEnabled: true,
      stationId: 'station1',
      displayOrder: 1,
      materialCode: 'C001',
      lastUpdated: '2024-01-15 14:30',
      status: 'normal'
    },
    {
      id: '2',
      identifier: '粉料2',
      name: '2#粉煤灰罐',
      type: 'powder',
      capacity: 80,
      current: 15,
      minLevel: 10,
      maxLevel: 75,
      isEnabled: true,
      stationId: 'station1',
      displayOrder: 2,
      materialCode: 'F001',
      lastUpdated: '2024-01-15 14:25',
      status: 'warning'
    },
    {
      id: '3',
      identifier: '骨料1',
      name: '1#砂罐',
      type: 'aggregate',
      capacity: 200,
      current: 150,
      minLevel: 20,
      maxLevel: 190,
      isEnabled: true,
      stationId: 'station1',
      displayOrder: 3,
      materialCode: 'S001',
      lastUpdated: '2024-01-15 14:35',
      status: 'normal'
    },
    {
      id: '4',
      identifier: '骨料2',
      name: '2#石罐',
      type: 'aggregate',
      capacity: 200,
      current: 5,
      minLevel: 20,
      maxLevel: 190,
      isEnabled: true,
      stationId: 'station1',
      displayOrder: 4,
      materialCode: 'G001',
      lastUpdated: '2024-01-15 14:20',
      status: 'critical'
    },
    {
      id: '5',
      identifier: '水1',
      name: '1#水罐',
      type: 'water',
      capacity: 50,
      current: 45,
      minLevel: 5,
      maxLevel: 48,
      isEnabled: true,
      stationId: 'station1',
      displayOrder: 5,
      lastUpdated: '2024-01-15 14:40',
      status: 'normal'
    },
    {
      id: '6',
      identifier: '外加剂1',
      name: '1#外加剂罐',
      type: 'additive',
      capacity: 10,
      current: 8,
      minLevel: 1,
      maxLevel: 9.5,
      isEnabled: true,
      stationId: 'station1',
      displayOrder: 6,
      materialCode: 'A001',
      lastUpdated: '2024-01-15 14:32',
      status: 'normal'
    }
  ]);

  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingSilo, setEditingSilo] = useState<Partial<SiloInfo>>({});
  const { toast } = useToast();

  const materialTypes = [
    { value: 'powder', label: '粉料', color: 'bg-orange-100 text-orange-800' },
    { value: 'aggregate', label: '骨料', color: 'bg-gray-100 text-gray-800' },
    { value: 'water', label: '水', color: 'bg-blue-100 text-blue-800' },
    { value: 'additive', label: '外加剂', color: 'bg-green-100 text-green-800' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      case 'offline': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'normal': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'offline': return <X className="h-4 w-4 text-gray-600" />;
      default: return null;
    }
  };

  const calculateStatus = (silo: SiloInfo): SiloInfo['status'] => {
    if (silo.current <= silo.minLevel) return 'critical';
    if (silo.current <= silo.minLevel * 1.5) return 'warning';
    return 'normal';
  };

  const filteredSilos = silos.filter(s => s.stationId === currentStation);

  const handleAdd = () => {
    const newSilo: SiloInfo = {
      id: Date.now().toString(),
      identifier: `新料仓${filteredSilos.length + 1}`,
      name: '',
      type: 'powder',
      capacity: 100,
      current: 0,
      minLevel: 10,
      maxLevel: 95,
      isEnabled: true,
      stationId: currentStation,
      displayOrder: filteredSilos.length + 1,
      lastUpdated: new Date().toLocaleString(),
      status: 'normal'
    };
    setEditingId(newSilo.id);
    setEditingSilo(newSilo);
    setSilos(prev => [...prev, newSilo]);
  };

  const handleEdit = (silo: SiloInfo) => {
    setEditingId(silo.id);
    setEditingSilo({ ...silo });
  };

  const handleSave = () => {
    if (!editingId || !editingSilo.name?.trim()) {
      toast({
        title: '保存失败',
        description: '请填写完整的料仓信息',
        variant: 'destructive'
      });
      return;
    }

    const updatedSilos = silos.map(s => 
      s.id === editingId ? { 
        ...s, 
        ...editingSilo,
        status: calculateStatus({ ...s, ...editingSilo } as SiloInfo),
        lastUpdated: new Date().toLocaleString()
      } as SiloInfo : s
    );
    
    setSilos(updatedSilos);
    setEditingId(null);
    setEditingSilo({});
    onSilosChange?.(updatedSilos);
    
    toast({
      title: '保存成功',
      description: '料仓信息已更新'
    });
  };

  const handleCancel = () => {
    if (editingSilo.name === '') {
      const updatedSilos = silos.filter(s => s.id !== editingId);
      setSilos(updatedSilos);
    }
    setEditingId(null);
    setEditingSilo({});
  };

  const handleDelete = (id: string) => {
    const updatedSilos = silos.filter(s => s.id !== id);
    setSilos(updatedSilos);
    onSilosChange?.(updatedSilos);
    
    toast({
      title: '删除成功',
      description: '料仓已删除'
    });
  };

  const handleRefresh = () => {
    // 模拟刷新料仓数据
    const updatedSilos = silos.map(silo => ({
      ...silo,
      current: Math.max(0, silo.current + (Math.random() - 0.5) * 10),
      lastUpdated: new Date().toLocaleString(),
      status: calculateStatus(silo)
    }));
    setSilos(updatedSilos);
    onSilosChange?.(updatedSilos);
    
    toast({
      title: '刷新成功',
      description: '料仓数据已更新'
    });
  };

  const handleToggleEnabled = (id: string, isEnabled: boolean) => {
    const updatedSilos = silos.map(s => 
      s.id === id ? { ...s, isEnabled, lastUpdated: new Date().toLocaleString() } : s
    );
    setSilos(updatedSilos);
    onSilosChange?.(updatedSilos);
  };

  return (
    <div className="space-y-4">
      <Tabs defaultValue="list" className="space-y-4">
        <TabsList>
          <TabsTrigger value="list">料仓列表</TabsTrigger>
          <TabsTrigger value="monitor">实时监控</TabsTrigger>
          <TabsTrigger value="settings">配置管理</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          {/* 操作栏 */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <h3 className="text-lg font-medium">料仓管理</h3>
                  <Badge variant="outline">当前站点: 搅拌站A</Badge>
                </div>
                <div className="flex space-x-2">
                  <Button size="sm" onClick={handleAdd}>
                    <Plus className="mr-2 h-4 w-4" />
                    新增料仓
                  </Button>
                  <Button size="sm" variant="outline" onClick={handleRefresh}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    刷新数据
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 料仓表格 */}
          <Card>
            <CardContent className="p-0">
              <div className="max-h-96 overflow-auto">
                <Table>
                  <TableHeader className="sticky top-0 bg-background">
                    <TableRow>
                      <TableHead className="w-16">启用</TableHead>
                      <TableHead>标识</TableHead>
                      <TableHead>名称</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>容量</TableHead>
                      <TableHead>当前量</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>使用率</TableHead>
                      <TableHead>更新时间</TableHead>
                      <TableHead className="w-32">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSilos.map((silo) => (
                      <TableRow key={silo.id} className={!silo.isEnabled ? 'opacity-60' : ''}>
                        <TableCell>
                          <Checkbox
                            checked={silo.isEnabled}
                            onCheckedChange={(checked) => handleToggleEnabled(silo.id, checked as boolean)}
                          />
                        </TableCell>
                        
                        <TableCell>
                          <code className="text-xs bg-muted px-1 py-0.5 rounded">
                            {silo.identifier}
                          </code>
                        </TableCell>
                        
                        <TableCell>
                          {editingId === silo.id ? (
                            <Input
                              value={editingSilo.name || ''}
                              onChange={(e) => setEditingSilo({
                                ...editingSilo,
                                name: e.target.value
                              })}
                              className="h-8"
                              placeholder="料仓名称"
                            />
                          ) : (
                            <span className="font-medium">{silo.name}</span>
                          )}
                        </TableCell>
                        
                        <TableCell>
                          {editingId === silo.id ? (
                            <Select
                              value={editingSilo.type || silo.type}
                              onValueChange={(value: any) => setEditingSilo({
                                ...editingSilo,
                                type: value
                              })}
                            >
                              <SelectTrigger className="h-8 w-24">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {materialTypes.map((type) => (
                                  <SelectItem key={type.value} value={type.value}>
                                    {type.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          ) : (
                            <Badge className={materialTypes.find(t => t.value === silo.type)?.color}>
                              {materialTypes.find(t => t.value === silo.type)?.label}
                            </Badge>
                          )}
                        </TableCell>
                        
                        <TableCell>
                          {editingId === silo.id ? (
                            <Input
                              type="number"
                              value={editingSilo.capacity || ''}
                              onChange={(e) => setEditingSilo({
                                ...editingSilo,
                                capacity: Number(e.target.value)
                              })}
                              className="h-8 w-20"
                              placeholder="容量"
                            />
                          ) : (
                            `${silo.capacity}t`
                          )}
                        </TableCell>
                        
                        <TableCell>
                          {editingId === silo.id ? (
                            <Input
                              type="number"
                              value={editingSilo.current || ''}
                              onChange={(e) => setEditingSilo({
                                ...editingSilo,
                                current: Number(e.target.value)
                              })}
                              className="h-8 w-20"
                              placeholder="当前量"
                            />
                          ) : (
                            <div className="flex items-center space-x-2">
                              <span>{silo.current}t</span>
                              {getStatusIcon(silo.status)}
                            </div>
                          )}
                        </TableCell>
                        
                        <TableCell>
                          <Badge className={getStatusColor(silo.status)}>
                            {silo.status === 'normal' && '正常'}
                            {silo.status === 'warning' && '警告'}
                            {silo.status === 'critical' && '严重'}
                            {silo.status === 'offline' && '离线'}
                          </Badge>
                        </TableCell>
                        
                        <TableCell>
                          <div className="w-20">
                            <Progress 
                              value={(silo.current / silo.capacity) * 100} 
                              className="h-2"
                            />
                            <div className="text-xs text-center mt-1">
                              {Math.round((silo.current / silo.capacity) * 100)}%
                            </div>
                          </div>
                        </TableCell>
                        
                        <TableCell className="text-xs text-muted-foreground">
                          {silo.lastUpdated}
                        </TableCell>
                        
                        <TableCell>
                          {editingId === silo.id ? (
                            <div className="flex space-x-1">
                              <Button size="sm" onClick={handleSave}>
                                <Save className="h-3 w-3" />
                              </Button>
                              <Button size="sm" variant="outline" onClick={handleCancel}>
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ) : (
                            <div className="flex space-x-1">
                              <Button size="sm" variant="outline" onClick={() => handleEdit(silo)}>
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button 
                                size="sm" 
                                variant="destructive" 
                                onClick={() => handleDelete(silo.id)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitor" className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {filteredSilos.filter(s => s.isEnabled).map((silo) => (
              <Card key={silo.id} className={`border-l-4 ${
                silo.status === 'critical' ? 'border-l-red-500' :
                silo.status === 'warning' ? 'border-l-yellow-500' : 'border-l-green-500'
              }`}>
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{silo.name}</h4>
                      {getStatusIcon(silo.status)}
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>当前量:</span>
                        <span className="font-medium">{silo.current}t</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>容量:</span>
                        <span>{silo.capacity}t</span>
                      </div>
                      <Progress 
                        value={(silo.current / silo.capacity) * 100} 
                        className="h-2"
                      />
                      <div className="text-xs text-center text-muted-foreground">
                        使用率: {Math.round((silo.current / silo.capacity) * 100)}%
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>配置说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm">
                <div>
                  <h4 className="font-medium mb-2">料仓标识说明：</h4>
                  <ul className="space-y-1 list-disc list-inside text-muted-foreground">
                    <li>粉料1、粉料2：用于水泥、粉煤灰等粉状材料</li>
                    <li>骨料1、骨料2：用于砂子、石子等骨料材料</li>
                    <li>水1、水2：用于拌合用水</li>
                    <li>外加剂1、外加剂2：用于减水剂、防冻剂等外加剂</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">状态说明：</h4>
                  <ul className="space-y-1 list-disc list-inside text-muted-foreground">
                    <li>正常：料仓存量充足，可正常使用</li>
                    <li>警告：料仓存量偏低，需要关注</li>
                    <li>严重：料仓存量不足，需要立即补充</li>
                    <li>离线：料仓传感器离线或故障</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">使用说明：</h4>
                  <ul className="space-y-1 list-disc list-inside text-muted-foreground">
                    <li>启用料仓时，请务必勾选"启用"选项</li>
                    <li>设置合理的最小和最大存量阈值</li>
                    <li>定期刷新数据以获取最新状态</li>
                    <li>关注警告和严重状态的料仓，及时补充材料</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
