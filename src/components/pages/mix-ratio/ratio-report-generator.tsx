'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Download, 
  Printer, 
  Mail, 
  QrCode,
  BarChart3,
  Calendar,
  User,
  Building
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ReportData {
  taskInfo: {
    taskNumber: string;
    projectName: string;
    strength: string;
    slump: string;
    impermeability: string;
    freezeResistance: string;
  };
  parameters: {
    density: number;
    waterRatio: number;
    waterAmount: number;
    sandRatio: number;
    additiveRatio: number;
    flyashRatio: number;
    totalWeight: number;
    mixingTime: number;
  };
  materials: Array<{
    name: string;
    type: string;
    amount: number;
    waterContent: number;
    actualAmount: number;
    siloName: string;
  }>;
  qualityScore: number;
  strengthPrediction: number;
  totalCost: number;
  carbonFootprint: number;
  operator: string;
  station: string;
  timestamp: string;
}

interface RatioReportGeneratorProps {
  data: ReportData;
  onClose: () => void;
}

export function RatioReportGenerator({ data, onClose }: RatioReportGeneratorProps) {
  const [reportType, setReportType] = useState<'standard' | 'detailed' | 'summary'>('standard');
  const [includeQRCode, setIncludeQRCode] = useState(true);
  const [includeChart, setIncludeChart] = useState(true);
  const [includeSignature, setIncludeSignature] = useState(true);
  const [additionalNotes, setAdditionalNotes] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  const reportTypes = [
    { value: 'standard', label: '标准报告', description: '包含基本配比信息和参数' },
    { value: 'detailed', label: '详细报告', description: '包含完整的计算过程和质量分析' },
    { value: 'summary', label: '简要报告', description: '仅包含关键配比信息' }
  ];

  const generateReport = async (format: 'pdf' | 'excel' | 'word') => {
    setIsGenerating(true);
    
    try {
      // 模拟报告生成
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const reportContent = {
        type: reportType,
        format,
        data,
        options: {
          includeQRCode,
          includeChart,
          includeSignature,
          additionalNotes
        },
        generatedAt: new Date().toISOString()
      };

      // 模拟文件下载
      const blob = new Blob([JSON.stringify(reportContent, null, 2)], { 
        type: 'application/json' 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `ratio_report_${data.taskInfo.taskNumber}_${Date.now()}.${format}`;
      link.click();
      URL.revokeObjectURL(url);

      toast({
        title: '报告生成成功',
        description: `${reportTypes.find(t => t.value === reportType)?.label}已生成并下载`
      });
    } catch (error) {
      toast({
        title: '生成失败',
        description: '报告生成过程中出现错误',
        variant: 'destructive'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePrint = () => {
    // 打印预览
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(generatePrintContent());
      printWindow.document.close();
      printWindow.print();
    }
  };

  const generatePrintContent = () => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>混凝土配比报告 - ${data.taskInfo.taskNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; }
          .section { margin: 20px 0; }
          .table { width: 100%; border-collapse: collapse; }
          .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          .table th { background-color: #f5f5f5; }
          .signature { margin-top: 50px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>混凝土配比报告</h1>
          <p>任务编号: ${data.taskInfo.taskNumber} | 工程名称: ${data.taskInfo.projectName}</p>
        </div>
        
        <div class="section">
          <h2>基本信息</h2>
          <table class="table">
            <tr><td>强度等级</td><td>${data.taskInfo.strength}</td></tr>
            <tr><td>坍落度</td><td>${data.taskInfo.slump}mm</td></tr>
            <tr><td>搅拌站</td><td>${data.station}</td></tr>
            <tr><td>操作员</td><td>${data.operator}</td></tr>
            <tr><td>生成时间</td><td>${new Date(data.timestamp).toLocaleString()}</td></tr>
          </table>
        </div>
        
        <div class="section">
          <h2>配比参数</h2>
          <table class="table">
            <tr><td>密度</td><td>${data.parameters.density} t/m³</td></tr>
            <tr><td>水胶比</td><td>${data.parameters.waterRatio}</td></tr>
            <tr><td>用水量</td><td>${data.parameters.waterAmount} kg</td></tr>
            <tr><td>砂率</td><td>${data.parameters.sandRatio}%</td></tr>
            <tr><td>总重量</td><td>${data.parameters.totalWeight} kg</td></tr>
            <tr><td>搅拌时间</td><td>${data.parameters.mixingTime} 秒</td></tr>
          </table>
        </div>
        
        <div class="section">
          <h2>材料用量</h2>
          <table class="table">
            <tr><th>材料名称</th><th>理论用量(kg)</th><th>实际用量(kg)</th><th>料仓</th></tr>
            ${data.materials.map(m => `
              <tr>
                <td>${m.name}</td>
                <td>${m.amount}</td>
                <td>${m.actualAmount}</td>
                <td>${m.siloName}</td>
              </tr>
            `).join('')}
          </table>
        </div>
        
        ${includeSignature ? `
        <div class="signature">
          <table style="width: 100%;">
            <tr>
              <td>技术负责人: _______________</td>
              <td>质检员: _______________</td>
              <td>日期: _______________</td>
            </tr>
          </table>
        </div>
        ` : ''}
      </body>
      </html>
    `;
  };

  const handleEmail = () => {
    const subject = `混凝土配比报告 - ${data.taskInfo.taskNumber}`;
    const body = `
任务编号: ${data.taskInfo.taskNumber}
工程名称: ${data.taskInfo.projectName}
强度等级: ${data.taskInfo.strength}
生成时间: ${new Date(data.timestamp).toLocaleString()}

请查看附件中的详细配比报告。
    `;
    
    const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(mailtoLink);
  };

  return (
    <div className="space-y-6">
      {/* 报告配置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            报告配置
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>报告类型</Label>
              <Select value={reportType} onValueChange={(value: any) => setReportType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {reportTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div>
                        <div className="font-medium">{type.label}</div>
                        <div className="text-xs text-muted-foreground">{type.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-3">
              <Label>报告选项</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="qrcode"
                    checked={includeQRCode}
                    onCheckedChange={setIncludeQRCode}
                  />
                  <Label htmlFor="qrcode" className="text-sm">包含二维码</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="chart"
                    checked={includeChart}
                    onCheckedChange={setIncludeChart}
                  />
                  <Label htmlFor="chart" className="text-sm">包含图表</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="signature"
                    checked={includeSignature}
                    onCheckedChange={setIncludeSignature}
                  />
                  <Label htmlFor="signature" className="text-sm">包含签名栏</Label>
                </div>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>附加说明</Label>
            <Textarea
              placeholder="输入附加说明或备注信息..."
              value={additionalNotes}
              onChange={(e) => setAdditionalNotes(e.target.value)}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* 报告预览 */}
      <Tabs defaultValue="preview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="preview">报告预览</TabsTrigger>
          <TabsTrigger value="data">数据详情</TabsTrigger>
        </TabsList>

        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-center">混凝土配比报告</CardTitle>
              <div className="text-center text-sm text-muted-foreground">
                任务编号: {data.taskInfo.taskNumber} | 工程名称: {data.taskInfo.projectName}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 基本信息 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <Label className="text-xs text-muted-foreground">强度等级</Label>
                  <div className="font-medium">{data.taskInfo.strength}</div>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">坍落度</Label>
                  <div className="font-medium">{data.taskInfo.slump}mm</div>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">搅拌站</Label>
                  <div className="font-medium">{data.station}</div>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">操作员</Label>
                  <div className="font-medium">{data.operator}</div>
                </div>
              </div>

              {/* 关键指标 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-3 text-center">
                    <div className="text-lg font-bold text-blue-600">{data.strengthPrediction}</div>
                    <div className="text-xs text-muted-foreground">预测强度(MPa)</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-3 text-center">
                    <div className="text-lg font-bold text-green-600">{data.qualityScore}</div>
                    <div className="text-xs text-muted-foreground">质量评分</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-3 text-center">
                    <div className="text-lg font-bold text-purple-600">¥{data.totalCost}</div>
                    <div className="text-xs text-muted-foreground">成本(元/m³)</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-3 text-center">
                    <div className="text-lg font-bold text-orange-600">{data.carbonFootprint}</div>
                    <div className="text-xs text-muted-foreground">碳足迹(kg)</div>
                  </CardContent>
                </Card>
              </div>

              {/* 材料用量表 */}
              <div>
                <Label className="text-sm font-medium mb-2 block">材料用量</Label>
                <div className="border rounded">
                  <table className="w-full text-sm">
                    <thead className="bg-muted">
                      <tr>
                        <th className="p-2 text-left">材料名称</th>
                        <th className="p-2 text-right">理论用量(kg)</th>
                        <th className="p-2 text-right">实际用量(kg)</th>
                        <th className="p-2 text-left">料仓</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.materials.map((material, index) => (
                        <tr key={index} className="border-t">
                          <td className="p-2 font-medium">{material.name}</td>
                          <td className="p-2 text-right">{material.amount}</td>
                          <td className="p-2 text-right">{material.actualAmount}</td>
                          <td className="p-2">{material.siloName}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {includeQRCode && (
                <div className="flex justify-center">
                  <div className="border-2 border-dashed border-muted-foreground p-4 rounded">
                    <QrCode className="h-16 w-16 text-muted-foreground" />
                    <div className="text-xs text-center mt-2">二维码</div>
                  </div>
                </div>
              )}

              {includeSignature && (
                <div className="border-t pt-4 mt-6">
                  <div className="grid grid-cols-3 gap-8 text-sm">
                    <div>
                      <div className="border-b border-gray-300 pb-1 mb-2">技术负责人</div>
                      <div className="text-xs text-muted-foreground">签名: _______________</div>
                    </div>
                    <div>
                      <div className="border-b border-gray-300 pb-1 mb-2">质检员</div>
                      <div className="text-xs text-muted-foreground">签名: _______________</div>
                    </div>
                    <div>
                      <div className="border-b border-gray-300 pb-1 mb-2">日期</div>
                      <div className="text-xs text-muted-foreground">_______________</div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>完整数据</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-xs bg-muted p-4 rounded overflow-auto max-h-96">
                {JSON.stringify(data, null, 2)}
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 操作按钮 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              选择操作方式生成或分享配比报告
            </div>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handlePrint}
                disabled={isGenerating}
              >
                <Printer className="mr-2 h-4 w-4" />
                打印
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleEmail}
                disabled={isGenerating}
              >
                <Mail className="mr-2 h-4 w-4" />
                邮件
              </Button>
              <Button 
                size="sm" 
                onClick={() => generateReport('pdf')}
                disabled={isGenerating}
              >
                <Download className="mr-2 h-4 w-4" />
                {isGenerating ? '生成中...' : '生成PDF'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
