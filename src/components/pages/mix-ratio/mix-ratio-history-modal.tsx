'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { X, History, FileText } from 'lucide-react';

import type { Task } from '@/types';
import type { MixRatioHistory, MixingStation } from '@/types/mixRatio';

interface MixRatioHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  task: Task;
}

export function MixRatioHistoryModal({ isOpen, onClose, task }: MixRatioHistoryModalProps) {
  const [selectedStation, setSelectedStation] = useState<string>('');
  const [historyRecords, setHistoryRecords] = useState<MixRatioHistory[]>([]);
  const [stations, setStations] = useState<MixingStation[]>([]);

  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen, task.id]);

  const loadData = async () => {
    // 模拟加载搅拌站数据
    const stationsData: MixingStation[] = [
      { id: '1', name: '搅拌站A', code: 'STA', isActive: true },
      { id: '2', name: '搅拌站B', code: 'STB', isActive: true },
    ];
    setStations(stationsData);
    setSelectedStation(stationsData[0]?.id || '');

    // 模拟加载历史记录
    const historyData: MixRatioHistory[] = [
      {
        id: '1',
        mixRatioId: 'ratio-1',
        taskId: task.id,
        operator: '张三',
        modifiedAt: '2024-01-15 10:30:00',
        price: 285.50,
        materials: {
          '水泥': 400,
          '超细粉': 0,
          '粉煤灰': 0,
          '中砂': 650,
          '5-25石子': 1100,
          '水': 180,
          '外加剂': 5.2
        },
        changes: '初始配比设置'
      },
      {
        id: '2',
        mixRatioId: 'ratio-1',
        taskId: task.id,
        operator: '李四',
        modifiedAt: '2024-01-15 14:20:00',
        price: 290.00,
        materials: {
          '水泥': 420,
          '超细粉': 0,
          '粉煤灰': 0,
          '中砂': 630,
          '5-25石子': 1080,
          '水': 185,
          '外加剂': 5.5
        },
        changes: '调整水泥用量和外加剂比例'
      },
      {
        id: '3',
        mixRatioId: 'ratio-1',
        taskId: task.id,
        operator: '王五',
        modifiedAt: '2024-01-15 16:45:00',
        price: 288.75,
        materials: {
          '水泥': 410,
          '超细粉': 20,
          '粉煤灰': 0,
          '中砂': 640,
          '5-25石子': 1090,
          '水': 182,
          '外加剂': 5.3
        },
        changes: '添加超细粉，优化配比结构'
      }
    ];
    setHistoryRecords(historyData);
  };

  const formatMaterialColumns = () => {
    // 获取所有材料类型
    const allMaterials = new Set<string>();
    historyRecords.forEach(record => {
      Object.keys(record.materials).forEach(material => {
        allMaterials.add(material);
      });
    });
    return Array.from(allMaterials).sort();
  };

  const materialColumns = formatMaterialColumns();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            任务配比历史记录
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 任务信息 */}
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">任务编号:</span>
                    <span className="font-medium">{task.taskNumber}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">工程名称:</span>
                    <span className="font-medium">{task.projectName}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">客户名称:</span>
                    <span className="font-medium">{task.customerName || '未设置'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">强度等级:</span>
                    <span className="font-medium">{task.strength}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">使用部位:</span>
                    <span className="font-medium">{task.constructionSite}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">施工单位:</span>
                    <span className="font-medium">{task.constructionUnit}</span>
                  </div>
                </div>
              </div>
              
              <Separator className="my-3" />
              
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium">选择搅拌站:</span>
                <Select value={selectedStation} onValueChange={setSelectedStation}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="选择搅拌站" />
                  </SelectTrigger>
                  <SelectContent>
                    {stations.map((station) => (
                      <SelectItem key={station.id} value={station.id}>
                        {station.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* 历史记录表格 */}
          <div className="border rounded-lg overflow-hidden">
            <div className="max-h-96 overflow-auto">
              <Table>
                <TableHeader className="sticky top-0 bg-background">
                  <TableRow>
                    <TableHead className="w-20">录入人</TableHead>
                    <TableHead className="w-32">修改时间</TableHead>
                    <TableHead className="w-20">价格</TableHead>
                    {materialColumns.map((material) => (
                      <TableHead key={material} className="w-20 text-center">
                        {material}
                      </TableHead>
                    ))}
                    <TableHead className="w-48">修改说明</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {historyRecords.map((record) => (
                    <TableRow key={record.id} className="text-sm">
                      <TableCell className="font-medium">{record.operator}</TableCell>
                      <TableCell className="text-muted-foreground">
                        {record.modifiedAt}
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        ¥{record.price.toFixed(2)}
                      </TableCell>
                      {materialColumns.map((material) => (
                        <TableCell key={material} className="text-center">
                          {record.materials[material] || '-'}
                        </TableCell>
                      ))}
                      <TableCell className="text-muted-foreground">
                        {record.changes}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    {historyRecords.length}
                  </div>
                  <div className="text-sm text-muted-foreground">修改次数</div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    ¥{historyRecords.length > 0 ? historyRecords[historyRecords.length - 1].price.toFixed(2) : '0.00'}
                  </div>
                  <div className="text-sm text-muted-foreground">当前价格</div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {materialColumns.length}
                  </div>
                  <div className="text-sm text-muted-foreground">材料种类</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 底部操作按钮 */}
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline">
            <FileText className="mr-2 h-4 w-4" />
            导出记录
          </Button>
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
