'use client';

import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Search, 
  Plus, 
  Minus, 
  Package,
  Layers,
  Droplets,
  Zap
} from 'lucide-react';
import type { Material, MaterialType } from '@/types/mixRatio';

interface MaterialSelectorProps {
  materials: Material[];
  selectedMaterialId?: string;
  onMaterialSelect: (materialId: string) => void;
  onMaterialAdd?: () => void;
  placeholder?: string;
  className?: string;
  showAddButton?: boolean;
}

interface NumberInputWithControlsProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  unit: string;
  step?: number;
  min?: number;
  max?: number;
  precision?: number;
}

export function NumberInputWithControls({
  label,
  value,
  onChange,
  unit,
  step = 1,
  min = 0,
  max,
  precision = 2
}: NumberInputWithControlsProps) {
  const adjustValue = (delta: number) => {
    const newValue = Math.max(min, value + delta);
    if (max !== undefined) {
      onChange(Math.min(max, newValue));
    } else {
      onChange(newValue);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseFloat(e.target.value) || 0;
    onChange(Math.max(min, max !== undefined ? Math.min(max, newValue) : newValue));
  };

  return (
    <div className="space-y-1">
      <Label className="text-xs text-muted-foreground">{label}</Label>
      <div className="flex items-center space-x-1">
        <Button
          variant="outline"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={() => adjustValue(-step)}
          disabled={value <= min}
        >
          <Minus className="h-3 w-3" />
        </Button>
        <Input
          type="number"
          step={step}
          min={min}
          max={max}
          value={value.toFixed(precision)}
          onChange={handleInputChange}
          className="h-6 text-xs text-center flex-1"
        />
        <Button
          variant="outline"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={() => adjustValue(step)}
          disabled={max !== undefined && value >= max}
        >
          <Plus className="h-3 w-3" />
        </Button>
        <span className="text-xs text-muted-foreground w-8">{unit}</span>
      </div>
    </div>
  );
}

export function MaterialSelector({
  materials,
  selectedMaterialId,
  onMaterialSelect,
  onMaterialAdd,
  placeholder = "选择材料",
  className = "",
  showAddButton = false
}: MaterialSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<MaterialType | 'all'>('all');

  const materialTypes: Array<{ value: MaterialType | 'all'; label: string; icon: React.ReactNode }> = [
    { value: 'all', label: '全部', icon: <Package className="h-3 w-3" /> },
    { value: 'cement', label: '水泥', icon: <Layers className="h-3 w-3" /> },
    { value: 'water', label: '水', icon: <Droplets className="h-3 w-3" /> },
    { value: 'sand', label: '砂', icon: <Package className="h-3 w-3" /> },
    { value: 'stone', label: '石子', icon: <Package className="h-3 w-3" /> },
    { value: 'additive', label: '外加剂', icon: <Zap className="h-3 w-3" /> },
    { value: 'admixture', label: '掺合料', icon: <Layers className="h-3 w-3" /> },
  ];

  const filteredMaterials = useMemo(() => {
    return materials.filter(material => {
      // 搜索匹配：名称、编码、规格
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch = searchTerm === '' ||
        material.name.toLowerCase().includes(searchLower) ||
        material.code.toLowerCase().includes(searchLower) ||
        material.specification.toLowerCase().includes(searchLower);

      // 类型匹配
      const matchesType = selectedType === 'all' || material.type === selectedType;

      return matchesSearch && matchesType;
    }).sort((a, b) => {
      // 优先显示名称匹配的材料
      if (searchTerm) {
        const aNameMatch = a.name.toLowerCase().includes(searchTerm.toLowerCase());
        const bNameMatch = b.name.toLowerCase().includes(searchTerm.toLowerCase());
        if (aNameMatch && !bNameMatch) return -1;
        if (!aNameMatch && bNameMatch) return 1;
      }

      // 按序号排序
      return (a.sequence || 0) - (b.sequence || 0);
    });
  }, [materials, searchTerm, selectedType]);

  const selectedMaterial = materials.find(m => m.id === selectedMaterialId);

  const getMaterialTypeInfo = (type: MaterialType) => {
    const typeInfo = materialTypes.find(t => t.value === type);
    return typeInfo || { value: type, label: type, icon: <Package className="h-3 w-3" /> };
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {/* 搜索和筛选 */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
          <Input
            placeholder="搜索材料..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-7 text-xs pl-7"
          />
        </div>
        
        <Select value={selectedType} onValueChange={(value) => setSelectedType(value as MaterialType | 'all')}>
          <SelectTrigger className="w-24 h-7 text-xs">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {materialTypes.map((type) => (
              <SelectItem key={type.value} value={type.value}>
                <div className="flex items-center space-x-1">
                  {type.icon}
                  <span>{type.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {showAddButton && onMaterialAdd && (
          <Button
            variant="outline"
            size="sm"
            onClick={onMaterialAdd}
            className="h-7 text-xs"
          >
            <Plus className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* 材料选择 */}
      <Select value={selectedMaterialId} onValueChange={onMaterialSelect}>
        <SelectTrigger className="h-8 text-xs">
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent className="max-h-60">
          {filteredMaterials.length > 0 ? (
            filteredMaterials.map((material) => {
              const typeInfo = getMaterialTypeInfo(material.type);
              return (
                <SelectItem key={material.id} value={material.id}>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center space-x-2">
                      {typeInfo.icon}
                      <span className="font-medium">{material.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {material.code}
                      </Badge>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {material.specification}
                    </span>
                  </div>
                </SelectItem>
              );
            })
          ) : (
            <div className="p-2 text-center text-xs text-muted-foreground">
              未找到匹配的材料
            </div>
          )}
        </SelectContent>
      </Select>

      {/* 选中材料信息 */}
      {selectedMaterial && (
        <Card className="border-dashed">
          <CardContent className="p-2">
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center space-x-2">
                {getMaterialTypeInfo(selectedMaterial.type).icon}
                <span className="font-medium">{selectedMaterial.name}</span>
                <Badge variant="secondary" className="text-xs">
                  {selectedMaterial.specification}
                </Badge>
              </div>
              <div className="text-muted-foreground">
                密度: {selectedMaterial.density} kg/m³
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
