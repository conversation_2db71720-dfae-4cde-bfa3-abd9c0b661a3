'use client';

import React, { useEffect } from 'react';

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  action: () => void;
  description: string;
}

interface KeyboardShortcutsProps {
  shortcuts: KeyboardShortcut[];
  enabled?: boolean;
}

export function KeyboardShortcuts({ shortcuts, enabled = true }: KeyboardShortcutsProps) {
  useEffect(() => {
    if (!enabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // 忽略在输入框中的按键
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
        return;
      }

      const matchingShortcut = shortcuts.find(shortcut => {
        return (
          event.key.toLowerCase() === shortcut.key.toLowerCase() &&
          !!event.ctrlKey === !!shortcut.ctrlKey &&
          !!event.shiftKey === !!shortcut.shiftKey &&
          !!event.altKey === !!shortcut.altKey
        );
      });

      if (matchingShortcut) {
        event.preventDefault();
        matchingShortcut.action();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts, enabled]);

  return null; // 这个组件不渲染任何内容
}

/**
 * 格式化快捷键显示
 */
export function formatShortcut(shortcut: KeyboardShortcut): string {
  const parts: string[] = [];
  
  if (shortcut.ctrlKey) parts.push('Ctrl');
  if (shortcut.shiftKey) parts.push('Shift');
  if (shortcut.altKey) parts.push('Alt');
  parts.push(shortcut.key.toUpperCase());
  
  return parts.join(' + ');
}

/**
 * 预定义的配比页面快捷键
 */
export function useMixRatioShortcuts({
  onSave,
  onSubmit,
  onAddMaterial,
  onReverseCalculation,
  onOpenGuide,
  onOpenMaterialManagement,
  onOpenSiloManagement,
  onOpenHistory
}: {
  onSave?: () => void;
  onSubmit?: () => void;
  onAddMaterial?: () => void;
  onReverseCalculation?: () => void;
  onOpenGuide?: () => void;
  onOpenMaterialManagement?: () => void;
  onOpenSiloManagement?: () => void;
  onOpenHistory?: () => void;
}) {
  const shortcuts: KeyboardShortcut[] = [
    // 保存和提交
    {
      key: 's',
      ctrlKey: true,
      action: () => onSave?.(),
      description: '保存配比'
    },
    {
      key: 's',
      ctrlKey: true,
      shiftKey: true,
      action: () => onSubmit?.(),
      description: '提交审核'
    },
    
    // 材料操作
    {
      key: 'a',
      ctrlKey: true,
      action: () => onAddMaterial?.(),
      description: '添加材料'
    },
    
    // 计算操作
    {
      key: 'r',
      ctrlKey: true,
      action: () => onReverseCalculation?.(),
      description: '执行反算'
    },
    
    // 帮助和管理
    {
      key: 'h',
      ctrlKey: true,
      action: () => onOpenGuide?.(),
      description: '打开操作指南'
    },
    {
      key: 'm',
      ctrlKey: true,
      action: () => onOpenMaterialManagement?.(),
      description: '材料管理'
    },
    {
      key: 'w',
      ctrlKey: true,
      action: () => onOpenSiloManagement?.(),
      description: '料仓管理'
    },
    {
      key: 'h',
      ctrlKey: true,
      shiftKey: true,
      action: () => onOpenHistory?.(),
      description: '配比历史'
    }
  ];

  return shortcuts;
}

/**
 * 快捷键帮助组件
 */
export function ShortcutHelp({ shortcuts }: { shortcuts: KeyboardShortcut[] }) {
  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">键盘快捷键</h4>
      <div className="space-y-1">
        {shortcuts.map((shortcut, index) => (
          <div key={index} className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">{shortcut.description}</span>
            <kbd className="px-2 py-1 bg-muted rounded text-xs font-mono">
              {formatShortcut(shortcut)}
            </kbd>
          </div>
        ))}
      </div>
    </div>
  );
}
