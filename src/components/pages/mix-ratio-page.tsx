'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Calculator, 
  History, 
  Settings, 
  RefreshCw, 
  Save, 
  Send, 
  X,
  Printer,
  Database,
  Users,
  CheckSquare
} from 'lucide-react';

import { OperationToolbar } from './mix-ratio/operation-toolbar';
import { TaskInfoSection } from './mix-ratio/task-info-section';
import { ModernCalculationTable } from './mix-ratio/modern-calculation-table';
import { MixRatioTable } from './mix-ratio/mix-ratio-table';
import { MaterialInfoPanel } from './mix-ratio/material-info-panel';
import { BottomActionBar } from './mix-ratio/bottom-action-bar';
import { MixRatioHistoryModal } from './mix-ratio/mix-ratio-history-modal';
import { MaterialManagementModal } from './mix-ratio/material-management-modal';
import { SiloManagementModal } from './mix-ratio/silo-management-modal';
import { QualityStandardModal } from './mix-ratio/quality-standard-modal';
import { ParameterSettingsModal } from './mix-ratio/parameter-settings-modal';

import type { Task } from '@/types';
import type { MixRatio, MixingStation, Material, SiloInfo, MaterialInfo } from '@/types/mixRatio';

interface MixRatioPageProps {
  task: Task;
  onClose: () => void;
}

export function MixRatioPage({ task, onClose }: MixRatioPageProps) {
  // 状态管理
  const [selectedStation, setSelectedStation] = useState<string>('');
  const [unifiedRatio, setUnifiedRatio] = useState(false);
  const [selectedRatioCode, setSelectedRatioCode] = useState<string>('');
  const [mixRatio, setMixRatio] = useState<MixRatio | null>(null);
  const [stations, setStations] = useState<MixingStation[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [silos, setSilos] = useState<SiloInfo[]>([]);
  const [materialInfos, setMaterialInfos] = useState<MaterialInfo[]>([]);

  // 模态框状态
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isMaterialModalOpen, setIsMaterialModalOpen] = useState(false);
  const [isSiloModalOpen, setIsSiloModalOpen] = useState(false);
  const [isQualityModalOpen, setIsQualityModalOpen] = useState(false);
  const [isParameterModalOpen, setIsParameterModalOpen] = useState(false);

  // 初始化数据
  useEffect(() => {
    loadInitialData();
  }, [task.id]);

  const loadInitialData = async () => {
    // 加载搅拌站列表
    const stationsData: MixingStation[] = [
      { id: '1', name: '搅拌站A', code: 'STA', isActive: true },
      { id: '2', name: '搅拌站B', code: 'STB', isActive: true },
    ];
    setStations(stationsData);
    setSelectedStation(stationsData[0]?.id || '');

    // 加载材料信息
    loadMaterials(stationsData[0]?.id || '');
    
    // 加载配比信息
    loadMixRatio();
  };

  const loadMaterials = async (stationId: string) => {
    // 模拟加载材料数据
    const materialsData: Material[] = [
      { id: '1', name: '水泥', code: 'C', type: 'cement', specification: 'P.O 42.5', density: 3100, sequence: 1, stationId },
      { id: '2', name: '水', code: 'W', type: 'water', specification: '自来水', density: 1000, sequence: 2, stationId },
      { id: '3', name: '沙子', code: 'S', type: 'sand', specification: '中砂', density: 2650, sequence: 3, stationId },
      { id: '4', name: '石子', code: 'G', type: 'stone', specification: '5-25mm', density: 2700, sequence: 4, stationId },
      { id: '5', name: '外加剂', code: 'A', type: 'additive', specification: '减水剂', density: 1200, sequence: 5, stationId },
    ];
    setMaterials(materialsData);

    // 加载料仓信息
    const silosData: SiloInfo[] = [
      { id: '1', identifier: '粉料1', name: '站1#水泥', displayOrder: 1, isEnabled: true, stationId, materialType: 'cement' },
      { id: '2', identifier: '骨料1', name: '站2#机制砂', displayOrder: 2, isEnabled: true, stationId, materialType: 'sand' },
      { id: '3', identifier: '骨料2', name: '站3#细石子', displayOrder: 3, isEnabled: true, stationId, materialType: 'stone' },
      { id: '4', identifier: '水1', name: '站1#污水', displayOrder: 4, isEnabled: true, stationId, materialType: 'water' },
      { id: '5', identifier: '外加剂1', name: '站1#减水剂', displayOrder: 5, isEnabled: true, stationId, materialType: 'additive' },
    ];
    setSilos(silosData);

    // 加载物料信息
    const materialInfosData: MaterialInfo[] = [
      { material: '水泥', specifications: ['P.O 42.5', 'P.O 52.5'], siloName: '站1#水泥', stationId, stationName: '搅拌站A' },
      { material: '沙子', specifications: ['中砂', '细砂'], siloName: '站2#机制砂', stationId, stationName: '搅拌站A' },
      { material: '石子', specifications: ['5-25mm', '10-30mm'], siloName: '站3#细石子', stationId, stationName: '搅拌站A' },
    ];
    setMaterialInfos(materialInfosData);
  };

  const loadMixRatio = async () => {
    // 模拟加载配比数据
    const mixRatioData: MixRatio = {
      id: '1',
      code: 'C125-00003',
      taskId: task.id,
      stationId: selectedStation,
      taskNumber: task.taskNumber,
      projectName: task.projectName,
      strength: task.strength,
      impermeability: '',
      freezeResistance: '',
      constructionSite: task.constructionSite,
      slump: 180,
      pouringMethod: task.pouringMethod,
      calculationParams: {
        density: 2.36,
        totalWeight: 2360,
        mixingTime: 95,
        waterCementRatio: 0.45,
        waterContent: 180,
        sandRatio: 35,
        additiveRatio: 1.3,
        antifreezeRatio: 0,
        flyashRatio: 0,
        slagRatio: 0,
        silicaRatio: 0,
        expansionRatio: 0,
        acceleratorRatio: 0,
        ultrafineRatio: 0,
      },
      calculationMethod: 'no_flyash_excess',
      items: [
        {
          id: '1',
          materialId: '1',
          materialName: '水泥',
          specification: 'P.O 42.5',
          theoreticalAmount: 400,
          waterContent: 0,
          stoneContent: 0,
          actualAmount: 400,
          designValue: 400,
          storageLocation: '站1#水泥',
          siloId: '1'
        },
        {
          id: '2',
          materialId: '2',
          materialName: '水',
          specification: '自来水',
          theoreticalAmount: 180,
          waterContent: 0,
          stoneContent: 0,
          actualAmount: 180,
          designValue: 180,
          storageLocation: '站1#污水',
          siloId: '4'
        }
      ],
      distributionRatio: {},
      productionTips: '',
      serialNumber: '',
      createdBy: '系统管理员',
      createdAt: new Date().toISOString(),
      lastModifiedBy: '系统管理员',
      lastModifiedAt: new Date().toISOString(),
      isSync: false,
      status: 'draft'
    };
    setMixRatio(mixRatioData);
    setSelectedRatioCode(mixRatioData.code);
  };

  const handleStationChange = (stationId: string) => {
    setSelectedStation(stationId);
    loadMaterials(stationId);
  };

  const handleSave = async () => {
    if (!mixRatio) return;
    
    try {
      // 保存配比数据
      console.log('保存配比:', mixRatio);
      // TODO: 调用API保存
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  const handleApproveAndSend = async () => {
    if (!mixRatio) return;
    
    try {
      // 审核并发送
      const updatedRatio = { ...mixRatio, status: 'sent' as const };
      setMixRatio(updatedRatio);
      console.log('审核发送:', updatedRatio);
      // TODO: 调用API
    } catch (error) {
      console.error('审核发送失败:', error);
    }
  };

  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col">
      {/* 页面标题栏 */}
      <div className="h-10 border-b bg-muted/50 flex items-center justify-end px-4 flex-shrink-0">
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* 操作栏 */}
      <OperationToolbar
        selectedStation={selectedStation}
        stations={stations.map(s => ({ ...s, isActive: true }))}
        onStationChange={handleStationChange}
        onMaterialManagement={() => setIsMaterialModalOpen(true)}
        onSiloManagement={() => setIsSiloModalOpen(true)}
        onRatioHistory={() => setIsHistoryModalOpen(true)}
        onParameterSettings={() => setIsParameterModalOpen(true)}
        onQualityStandards={() => setIsQualityModalOpen(true)}
        onUnifiedRatio={() => setUnifiedRatio(!unifiedRatio)}
      />

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧内容 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 overflow-y-auto p-3 space-y-3">
            {/* 任务信息 */}
            {mixRatio && (
              <TaskInfoSection
                mixRatio={mixRatio}
                onUpdate={setMixRatio}
              />
            )}

            {/* 计算部分 */}
            {mixRatio && (
              <ModernCalculationTable
                params={mixRatio.calculationParams}
                calculationMethod={mixRatio.calculationMethod}
                onParamsChange={(params) => setMixRatio({
                  ...mixRatio,
                  calculationParams: { ...mixRatio.calculationParams, ...params }
                })}
                onMethodChange={(method) => setMixRatio({
                  ...mixRatio,
                  calculationMethod: method
                })}
                onCalculate={() => {
                  // TODO: 实现反算逻辑
                  console.log('执行反算');
                }}
                onApply={() => {
                  // TODO: 应用计算结果
                  console.log('应用计算结果');
                }}
                onOpenCalculationBook={() => {
                  // TODO: 打开配比计算书
                  console.log('打开配比计算书');
                }}
              />
            )}

            {/* 配比表格 */}
            {mixRatio && (
              <MixRatioTable
                mixRatio={mixRatio}
                onUpdate={setMixRatio}
                materials={materials}
                silos={silos}
                onOpenQualityStandard={() => setIsQualityModalOpen(true)}
              />
            )}
          </div>

          {/* 底部操作栏 */}
          {mixRatio && (
            <BottomActionBar
              productionTips={mixRatio.productionTips}
              serialNumber={mixRatio.serialNumber}
              createdBy={mixRatio.createdBy}
              createdAt={mixRatio.createdAt}
              lastModifiedBy={mixRatio.lastModifiedBy}
              lastModifiedAt={mixRatio.lastModifiedAt}
              isSync={mixRatio.isSync}
              status={mixRatio.status}
              onProductionTipsChange={(tips) => setMixRatio({
                ...mixRatio,
                productionTips: tips
              })}
              onSerialNumberChange={(serialNumber) => setMixRatio({
                ...mixRatio,
                serialNumber
              })}
              onSyncChange={(isSync) => setMixRatio({
                ...mixRatio,
                isSync
              })}
              onSave={handleSave}
              onSubmit={handleApproveAndSend}
              onExit={onClose}
            />
          )}
        </div>

        {/* 右侧物料信息 */}
        <MaterialInfoPanel
          materialInfos={materialInfos}
          selectedStation={selectedStation}
          stations={stations}
          silos={silos}
        />
      </div>

      {/* 模态框 */}
      <MixRatioHistoryModal
        isOpen={isHistoryModalOpen}
        onClose={() => setIsHistoryModalOpen(false)}
        task={task}
      />

      <MaterialManagementModal
        isOpen={isMaterialModalOpen}
        onClose={() => setIsMaterialModalOpen(false)}
        selectedStation={selectedStation}
        stations={stations}
        materials={materials}
        onMaterialsChange={setMaterials}
      />

      <SiloManagementModal
        isOpen={isSiloModalOpen}
        onClose={() => setIsSiloModalOpen(false)}
        selectedStation={selectedStation}
        stations={stations}
        silos={silos}
        onSilosChange={setSilos}
      />

      <QualityStandardModal
        isOpen={isQualityModalOpen}
        onClose={() => setIsQualityModalOpen(false)}
      />

      <ParameterSettingsModal
        isOpen={isParameterModalOpen}
        onClose={() => setIsParameterModalOpen(false)}
      />
    </div>
  );
}
