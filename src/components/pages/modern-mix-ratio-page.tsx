'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  X,
  Calculator,
  AlertCircle,
  CheckCircle2,
  Clock
} from 'lucide-react';

import { OperationToolbar } from './mix-ratio/operation-toolbar';
import { TaskInfoSection } from './mix-ratio/task-info-section';
import { ModernCalculationTable } from './mix-ratio/modern-calculation-table';
import { MixRatioTable } from './mix-ratio/mix-ratio-table';
import { MaterialInfoPanel } from './mix-ratio/material-info-panel';
import { BottomActionBar } from './mix-ratio/bottom-action-bar';
import { MixRatioHistoryModal } from './mix-ratio/mix-ratio-history-modal';
import { MaterialManagementModal } from './mix-ratio/material-management-modal';
import { SiloManagementModal } from './mix-ratio/silo-management-modal';
import { QualityStandardModal } from './mix-ratio/quality-standard-modal';
import { ParameterSettingsModal } from './mix-ratio/parameter-settings-modal';

import type { Task } from '@/types';
import type { MixRatio, MixingStation, Material, SiloInfo, MaterialInfo } from '@/types/mixRatio';

interface ModernMixRatioPageProps {
  task: Task;
  onClose: () => void;
}

export function ModernMixRatioPage({ task, onClose }: ModernMixRatioPageProps) {
  // 状态管理
  const [selectedStation, setSelectedStation] = useState<string>('station-1');
  const [mixRatio, setMixRatio] = useState<MixRatio | null>(null);
  const [stations, setStations] = useState<MixingStation[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [silos, setSilos] = useState<SiloInfo[]>([]);
  const [materialInfos, setMaterialInfos] = useState<MaterialInfo[]>([]);

  // 模态框状态
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isMaterialModalOpen, setIsMaterialModalOpen] = useState(false);
  const [isSiloModalOpen, setIsSiloModalOpen] = useState(false);
  const [isQualityModalOpen, setIsQualityModalOpen] = useState(false);
  const [isParameterModalOpen, setIsParameterModalOpen] = useState(false);

  // 操作状态
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCalculating, setIsCalculating] = useState(false);

  // 初始化数据
  useEffect(() => {
    initializeData();
  }, [task]);

  const initializeData = () => {
    // 初始化搅拌站数据
    const mockStations: MixingStation[] = [
      { id: 'station-1', name: '搅拌站A', code: 'A01', isActive: true },
      { id: 'station-2', name: '搅拌站B', code: 'B01', isActive: true },
      { id: 'station-3', name: '搅拌站C', code: 'C01', isActive: false },
    ];
    setStations(mockStations);

    // 初始化配比数据
    const mockMixRatio: MixRatio = {
      id: `mix-${task.id}`,
      code: `MR-${task.taskNumber}`,
      taskId: task.id,
      stationId: selectedStation,
      taskNumber: task.taskNumber,
      projectName: task.projectName,
      strength: task.strength,
      impermeability: '',
      freezeResistance: '',
      constructionSite: task.constructionSite,
      slump: 180,
      pouringMethod: task.pouringMethod,
      calculationParams: {
        density: 2.4,
        totalWeight: 2400,
        mixingTime: 120,
        waterCementRatio: 0.45,
        waterContent: 180,
        sandRatio: 35,
        additiveRatio: 1.2,
        antifreezeRatio: 0,
        flyashRatio: 15,
        slagRatio: 10,
        silicaRatio: 0,
        expansionRatio: 0,
        acceleratorRatio: 0,
        ultrafineRatio: 0
      },
      calculationMethod: 'consider_flyash',
      items: [
        {
          id: '1',
          materialId: 'cement-1',
          materialName: 'P.O 42.5水泥',
          specification: 'P.O 42.5',
          theoreticalAmount: 350,
          waterContent: 0,
          stoneContent: 0,
          actualAmount: 350,
          designValue: 350,
          storageLocation: '1#水泥仓',
          siloId: 'silo-1'
        },
        {
          id: '2',
          materialId: 'water-1',
          materialName: '水',
          specification: '自来水',
          theoreticalAmount: 180,
          waterContent: 100,
          stoneContent: 0,
          actualAmount: 180,
          designValue: 180,
          storageLocation: '1#水箱',
          siloId: 'silo-2'
        }
      ],
      distributionRatio: {},
      productionTips: '',
      serialNumber: '',
      createdBy: 'system',
      createdAt: new Date().toISOString(),
      lastModifiedBy: 'system',
      lastModifiedAt: new Date().toISOString(),
      isSync: false,
      status: 'draft'
    };
    setMixRatio(mockMixRatio);

    // 初始化材料数据
    const mockMaterials: Material[] = [
      {
        id: 'cement-1',
        name: 'P.O 42.5水泥',
        code: 'C001',
        type: 'cement',
        specification: 'P.O 42.5',
        density: 3100,
        sequence: 1,
        stationId: selectedStation
      },
      {
        id: 'water-1',
        name: '水',
        code: 'W001',
        type: 'water',
        specification: '自来水',
        density: 1000,
        sequence: 2,
        stationId: selectedStation
      }
    ];
    setMaterials(mockMaterials);

    // 初始化料仓数据
    const mockSilos: SiloInfo[] = [
      {
        id: 'silo-1',
        identifier: '粉料1',
        name: '1#水泥仓',
        displayOrder: 1,
        isEnabled: true,
        stationId: selectedStation,
        materialType: 'cement'
      },
      {
        id: 'silo-2',
        identifier: '水1',
        name: '1#水箱',
        displayOrder: 2,
        isEnabled: true,
        stationId: selectedStation,
        materialType: 'water'
      }
    ];
    setSilos(mockSilos);
  };

  // 事件处理函数
  const handleStationChange = (stationId: string) => {
    setSelectedStation(stationId);
    // 重新加载该站点的数据
    // TODO: 实现站点切换逻辑
  };

  const handleCalculate = async () => {
    if (!mixRatio) return;
    
    setIsCalculating(true);
    try {
      // TODO: 实现配比反算逻辑
      await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟计算
      console.log('配比反算完成');
    } catch (error) {
      console.error('计算失败:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleSave = async () => {
    if (!mixRatio) return;
    
    setIsSaving(true);
    try {
      // TODO: 保存配比数据
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('保存成功');
    } catch (error) {
      console.error('保存失败:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSubmit = async () => {
    if (!mixRatio) return;
    
    setIsSubmitting(true);
    try {
      // TODO: 审核并发送
      await new Promise(resolve => setTimeout(resolve, 1500));
      setMixRatio({ ...mixRatio, status: 'sent' });
      console.log('审核发送成功');
    } catch (error) {
      console.error('审核发送失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'draft':
        return { label: '草稿', variant: 'secondary' as const, icon: <Clock className="h-4 w-4" /> };
      case 'approved':
        return { label: '已审核', variant: 'default' as const, icon: <CheckCircle2 className="h-4 w-4" /> };
      case 'sent':
        return { label: '已发送', variant: 'destructive' as const, icon: <AlertCircle className="h-4 w-4" /> };
      default:
        return { label: '未知', variant: 'outline' as const, icon: <AlertCircle className="h-4 w-4" /> };
    }
  };

  const statusInfo = mixRatio ? getStatusInfo(mixRatio.status) : null;

  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col">
      {/* 页面标题栏 */}
      <div className="h-12 border-b bg-gradient-to-r from-blue-50 to-green-50 flex items-center justify-between px-4 flex-shrink-0">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Calculator className="h-5 w-5 text-blue-600" />
            <h1 className="text-lg font-semibold text-gray-900">混凝土配比管理</h1>
          </div>
          <Separator orientation="vertical" className="h-6" />
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">任务:</span>
            <Badge variant="outline">{task.taskNumber}</Badge>
            <span className="text-sm font-medium">{task.projectName}</span>
          </div>
          {statusInfo && (
            <>
              <Separator orientation="vertical" className="h-6" />
              <Badge variant={statusInfo.variant} className="text-xs">
                {statusInfo.icon}
                <span className="ml-1">{statusInfo.label}</span>
              </Badge>
            </>
          )}
        </div>
        
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* 操作栏 */}
      <OperationToolbar
        selectedStation={selectedStation}
        stations={stations.map(s => ({ ...s, isActive: s.isActive }))}
        onStationChange={handleStationChange}
        onMaterialManagement={() => setIsMaterialModalOpen(true)}
        onSiloManagement={() => setIsSiloModalOpen(true)}
        onRatioHistory={() => setIsHistoryModalOpen(true)}
        onParameterSettings={() => setIsParameterModalOpen(true)}
        onQualityStandards={() => setIsQualityModalOpen(true)}
        onUnifiedRatio={() => console.log('统一配比')}
      />

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧内容 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {/* 任务信息 */}
            {mixRatio && (
              <TaskInfoSection
                mixRatio={mixRatio}
                onUpdate={setMixRatio}
              />
            )}

            {/* 计算参数 */}
            {mixRatio && (
              <ModernCalculationTable
                params={mixRatio.calculationParams}
                calculationMethod={mixRatio.calculationMethod}
                onParamsChange={(params) => setMixRatio({
                  ...mixRatio,
                  calculationParams: { ...mixRatio.calculationParams, ...params }
                })}
                onMethodChange={(method) => setMixRatio({
                  ...mixRatio,
                  calculationMethod: method
                })}
                onCalculate={handleCalculate}
                onApply={() => console.log('应用计算结果')}
                onOpenCalculationBook={() => console.log('打开配比计算书')}
                isCalculating={isCalculating}
              />
            )}

            {/* 配比表格 */}
            {mixRatio && (
              <MixRatioTable
                mixRatio={mixRatio}
                onUpdate={setMixRatio}
                materials={materials}
                silos={silos}
                onOpenQualityStandard={() => setIsQualityModalOpen(true)}
              />
            )}
          </div>

          {/* 底部操作栏 */}
          {mixRatio && (
            <BottomActionBar
              productionTips={mixRatio.productionTips}
              serialNumber={mixRatio.serialNumber}
              createdBy={mixRatio.createdBy}
              createdAt={mixRatio.createdAt}
              lastModifiedBy={mixRatio.lastModifiedBy}
              lastModifiedAt={mixRatio.lastModifiedAt}
              isSync={mixRatio.isSync}
              status={mixRatio.status}
              onProductionTipsChange={(tips) => setMixRatio({
                ...mixRatio,
                productionTips: tips
              })}
              onSerialNumberChange={(serialNumber) => setMixRatio({
                ...mixRatio,
                serialNumber
              })}
              onSyncChange={(isSync) => setMixRatio({
                ...mixRatio,
                isSync
              })}
              onSave={handleSave}
              onSubmit={handleSubmit}
              onExit={onClose}
              isSaving={isSaving}
              isSubmitting={isSubmitting}
            />
          )}
        </div>

        {/* 右侧物料信息面板 */}
        <div className="w-80 border-l bg-muted/20 flex-shrink-0">
          <MaterialInfoPanel
            materials={materialInfos}
            selectedStation={selectedStation}
            onStationChange={setSelectedStation}
          />
        </div>
      </div>

      {/* 模态框 */}
      {isHistoryModalOpen && (
        <MixRatioHistoryModal
          taskId={task.id}
          onClose={() => setIsHistoryModalOpen(false)}
        />
      )}

      {isMaterialModalOpen && (
        <MaterialManagementModal
            onClose={() => setIsMaterialModalOpen(false)}
            onMaterialsChange={setMaterials} isOpen={false} selectedStation={''} stations={[]} materials={[]}></MaterialManagementModal>
      )}

      {isSiloModalOpen && (
        <SiloManagementModal
          onClose={() => setIsSiloModalOpen(false)}
          onSilosChange={setSilos}
        />
      )}

      {isQualityModalOpen && (
        <QualityStandardModal
          onClose={() => setIsQualityModalOpen(false)}
        />
      )}

      {isParameterModalOpen && (
        <ParameterSettingsModal
          onClose={() => setIsParameterModalOpen(false)}
        />
      )}
    </div>
  );
}
