'use client';

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Virtuoso } from 'react-virtuoso';
import { cn } from '@/lib/utils';

// 导入增强型任务卡片组件
import { ConfigurableTaskCard } from './cards/ConfigurableTaskCard';
import { TaskCardConfigModal } from './cards/TaskCardConfigModal';
import { PerformanceMonitorPanel } from './cards/PerformanceMonitorPanel';
import { useCardPerformance } from './cards/useCardPerformance';
import { Button } from '@/components/ui/button';
import { Settings2, Grid3X3, LayoutGrid, Maximize2, Activity } from 'lucide-react';
import type { Task, Vehicle, TaskListStoredSettings, VehicleDisplayMode } from '@/types';
import type { TaskCardConfig } from '@/types/taskCardConfig';
import { defaultTaskCardConfig } from '@/types/taskCardConfig';

interface CardConfig {
  size: 'small' | 'medium' | 'large' | 'extra-large';
  layout: 'compact' | 'standard' | 'detailed' | 'minimal';
  theme: 'default' | 'modern' | 'glass' | 'gradient' | 'dark';
  spacing: 'tight' | 'normal' | 'loose';
  borderRadius: 'none' | 'small' | 'medium' | 'large' | 'full';
  shadow: 'none' | 'small' | 'medium' | 'large' | 'glow';
  animation: 'none' | 'subtle' | 'smooth' | 'bouncy';
  columns: 'auto' | '1' | '2' | '3' | '4' | '5' | '6';
}

interface EnhancedTaskCardViewProps {
  filteredTasks: Task[];
  vehicles: Vehicle[];
  settings: TaskListStoredSettings;
  productionLineCount: number;
  vehicleDisplayMode: VehicleDisplayMode;
  taskStatusFilter: string;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onOpenDeliveryOrderDetailsForVehicle: (vehicleId: string, taskId: string) => void;
  onOpenVehicleCardContextMenu: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onTaskContextMenu: (event: React.MouseEvent, taskId: string) => void;
  onTaskDoubleClick: (task: Task) => void;
  onOpenStyleEditor: () => void;
}

export const EnhancedTaskCardView: React.FC<EnhancedTaskCardViewProps> = ({
  filteredTasks,
  vehicles,
  settings,
  productionLineCount,
  vehicleDisplayMode,
  taskStatusFilter,
  onCancelVehicleDispatch,
  onOpenDeliveryOrderDetailsForVehicle,
  onOpenVehicleCardContextMenu,
  onTaskContextMenu,
  onTaskDoubleClick,
  onOpenStyleEditor,
}) => {
  const [cardConfig, setCardConfig] = useState<CardConfig>({
    size: 'small',
    layout: 'standard',
    theme: 'default',
    spacing: 'normal',
    borderRadius: 'medium',
    shadow: 'medium',
    animation: 'smooth',
    columns: 'auto',
  });

  // TaskCardConfig for ConfigurableTaskCard
  const [taskCardConfig, setTaskCardConfig] = useState<TaskCardConfig>(defaultTaskCardConfig);

  const [configModalOpen, setConfigModalOpen] = useState(false);
  const [performanceMonitorOpen, setPerformanceMonitorOpen] = useState(false);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1280);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 性能优化 Hook
  const {
    containerRef,
    performanceConfig,
    measureRender,
    shouldUseVirtualScroll,
    getVirtualScrollConfig,
  } = useCardPerformance({
    componentName: 'EnhancedTaskCardView',
    enableMonitoring: process.env.NODE_ENV === 'development',
  });

  // 获取网格列数 - 使用 useMemo 缓存计算结果
  const gridColumns = useMemo(() => {
    if (cardConfig.columns === 'auto') {
      switch (cardConfig.size) {
        case 'small': return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6';
        case 'medium': return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5';
        case 'large': return 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4';
        case 'extra-large': return 'grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3';
        default: return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
      }
    } else {
      return `grid-cols-${cardConfig.columns}`;
    }
  }, [cardConfig.size, cardConfig.columns]);

  // 获取间距样式 - 使用 useMemo 缓存
  const spacingClass = useMemo(() => {
    switch (cardConfig.spacing) {
      case 'tight': return 'gap-1 p-1';
      case 'loose': return 'gap-2 p-2';
      default: return 'gap-4 p-4';
    }
  }, [cardConfig.spacing]);

  // 获取容器主题样式 - 使用 useMemo 缓存
  const containerTheme = useMemo(() => {
    switch (cardConfig.theme) {
      case 'modern':
        return 'bg-gradient-to-br from-slate-50 to-gray-100 dark:from-slate-900 dark:to-gray-800';
      case 'glass':
        return 'bg-white/70 backdrop-blur-sm dark:bg-gray-900/70';
      case 'gradient':
        return 'bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-blue-900/20 dark:via-gray-900 dark:to-purple-900/20';
      case 'dark':
        return 'bg-gray-900 text-white';
      default:
        return 'bg-background';
    }
  }, [cardConfig.theme]);

  // 计算每行的列数 - 用于虚拟滚动
  const columnsPerRow = useMemo(() => {
    if (cardConfig.columns !== 'auto') {
      return parseInt(cardConfig.columns);
    }

    // 根据当前屏幕尺寸和卡片大小计算列数
    const getColumnsForCurrentScreen = () => {
      const width = windowWidth;

      switch (cardConfig.size) {
        case 'small':
          if (width >= 1536) return 6; // 2xl
          if (width >= 1280) return 5; // xl
          if (width >= 1024) return 4; // lg
          if (width >= 768) return 3;  // md
          if (width >= 640) return 2;  // sm
          return 1;
        case 'medium':
          if (width >= 1536) return 5; // 2xl
          if (width >= 1280) return 4; // xl
          if (width >= 1024) return 3; // lg
          if (width >= 768) return 2;  // md
          if (width >= 640) return 2;  // sm
          return 1;
        case 'large':
          if (width >= 1536) return 4; // 2xl
          if (width >= 1280) return 3; // xl
          if (width >= 1024) return 2; // lg
          if (width >= 768) return 2;  // md
          return 1;
        case 'extra-large':
          if (width >= 1536) return 3; // 2xl
          if (width >= 1280) return 2; // xl
          if (width >= 1024) return 2; // lg
          return 1;
        default:
          if (width >= 1280) return 4; // xl
          if (width >= 1024) return 3; // lg
          if (width >= 768) return 2;  // md
          if (width >= 640) return 2;  // sm
          return 1;
      }
    };

    return getColumnsForCurrentScreen();
  }, [cardConfig.size, cardConfig.columns, windowWidth]);

  // 计算行数
  const totalRows = useMemo(() => {
    return Math.ceil(filteredTasks.length / columnsPerRow);
  }, [filteredTasks.length, columnsPerRow]);

  // 处理任务右键菜单 - 使用 useCallback 优化
  const handleTaskContextMenu = useCallback((e: React.MouseEvent, task: Task) => {
    onTaskContextMenu(e, task.id);
  }, [onTaskContextMenu]);

  // 处理车辆双击 - 使用 useCallback 优化
  const handleVehicleDoubleClick = useCallback((vehicleId: string, taskId: string) => {
    onOpenDeliveryOrderDetailsForVehicle(vehicleId, taskId);
  }, [onOpenDeliveryOrderDetailsForVehicle]);

  // 缓存车辆分组，避免每次渲染时重新计算
  const vehiclesByTask = useMemo(() => {
    const map = new Map<string, Vehicle[]>();
    vehicles.forEach(vehicle => {
      if (vehicle.assignedTaskId) {
        if (!map.has(vehicle.assignedTaskId)) {
          map.set(vehicle.assignedTaskId, []);
        }
        map.get(vehicle.assignedTaskId)!.push(vehicle);
      }
    });
    return map;
  }, [vehicles]);

  // 渲染单行卡片 - 用于虚拟滚动，使用性能优化
  const renderRow = useCallback((index: number) => {
    const startIndex = index * columnsPerRow;
    const endIndex = Math.min(startIndex + columnsPerRow, filteredTasks.length);
    const rowTasks = filteredTasks.slice(startIndex, endIndex);

    return (
      <div
        key={index}
        className={cn(
          "grid w-full",
          gridColumns,
          spacingClass,
          // 性能优化类
          "virtual-row-performance"
        )}
        style={{
          minHeight: cardConfig.size === 'small' ? '220px' :
                     cardConfig.size === 'large' ? '370px' :
                     cardConfig.size === 'extra-large' ? '470px' : '300px',
          // 启用硬件加速
          transform: 'translateZ(0)',
          contain: 'layout style paint',
        }}
      >
        {rowTasks.map((task) => {
          const taskVehicles = vehiclesByTask.get(task.id) || [];

          return (
            <ConfigurableTaskCard
              key={task.id}
              task={task}
              vehicles={taskVehicles}
              config={taskCardConfig}
              vehicleDisplayMode={vehicleDisplayMode}
              inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
              density={settings.density}
              onCancelDispatch={onCancelVehicleDispatch}
              onOpenStyleEditor={onOpenStyleEditor}
              onOpenDeliveryOrderDetails={handleVehicleDoubleClick}
              onOpenVehicleContextMenu={onOpenVehicleCardContextMenu}
              onTaskContextMenu={handleTaskContextMenu}
              onTaskDoubleClick={onTaskDoubleClick}
            />
          );
        })}
      </div>
    );
  }, [
    columnsPerRow,
    filteredTasks,
    gridColumns,
    spacingClass,
    taskCardConfig,
    vehiclesByTask,
    vehicleDisplayMode,
    settings.inTaskVehicleCardStyles,
    settings.density,
    onCancelVehicleDispatch,
    onOpenStyleEditor,
    handleVehicleDoubleClick,
    onOpenVehicleCardContextMenu,
    handleTaskContextMenu,
    onTaskDoubleClick,
  ]);

  // 空状态显示
  if (filteredTasks.length === 0) {
    return (
      <div className={cn(
        "flex flex-col h-full transition-all duration-300",
        containerTheme
      )}>
        <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center gap-2">
            <LayoutGrid className="w-5 h-5 text-muted-foreground" />
            <span className="font-medium">任务卡片 (0)</span>
          </div>
        </div>

        <div className={cn(
          "flex flex-col items-center justify-center flex-1 text-muted-foreground",
          spacingClass
        )}>
          <div className="text-center space-y-4">
            <div className="w-24 h-24 mx-auto bg-muted rounded-full flex items-center justify-center">
              <LayoutGrid className="w-12 h-12 text-muted-foreground" />
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2">暂无任务</h3>
              <p className="text-sm text-muted-foreground">
                当前筛选条件下没有找到任务
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 获取虚拟滚动配置
  const virtualScrollConfig = getVirtualScrollConfig();
  const useVirtualScroll = shouldUseVirtualScroll(filteredTasks.length);

  return (
    <div
      ref={containerRef}
      className={cn(
        "flex flex-col h-full transition-all duration-300",
        containerTheme,
        // 性能优化类
        "transform-gpu"
      )}
      style={{
        // 启用 CSS 包含以提升性能
        contain: 'layout style paint',
      }}
    >
        {/* 工具栏 */}
       

        {/* 卡片网格 - 条件渲染虚拟滚动或标准网格 */}
        <div className="flex-1 overflow-hidden">
          {useVirtualScroll ? (
            <Virtuoso
              style={{ height: '100%' }}
              totalCount={totalRows}
              itemContent={renderRow}
              components={{
                Item: ({ children, ...props }) => (
                  <div {...props} className={cn("w-full", spacingClass)}>
                    {children}
                  </div>
                ),
              }}
              overscan={virtualScrollConfig.overscan}
              increaseViewportBy={virtualScrollConfig.increaseViewportBy}
            />
          ) : (
            <div className={cn("overflow-auto h-full card-grid-performance", spacingClass)}>
              <div className={cn("grid", gridColumns, spacingClass)}>
                {filteredTasks.map((task) => {
                  const taskVehicles = vehiclesByTask.get(task.id) || [];

                  return (
                    <ConfigurableTaskCard
              key={task.id}
              task={task}
              vehicles={taskVehicles}
              config={taskCardConfig}
              vehicleDisplayMode={vehicleDisplayMode}
              inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
              density={settings.density}
              onCancelDispatch={onCancelVehicleDispatch}
              onOpenStyleEditor={onOpenStyleEditor}
              onOpenDeliveryOrderDetails={handleVehicleDoubleClick}
              onOpenVehicleContextMenu={onOpenVehicleCardContextMenu}
              onTaskContextMenu={handleTaskContextMenu}
              onTaskDoubleClick={onTaskDoubleClick}
            />
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* 配置弹窗 */}
        <TaskCardConfigModal
          open={configModalOpen}
          onOpenChange={setConfigModalOpen}
          config={taskCardConfig}
          onConfigChange={setTaskCardConfig}
        />

        {/* 性能监控面板 */}
        <PerformanceMonitorPanel
          isVisible={performanceMonitorOpen}
          onClose={() => setPerformanceMonitorOpen(false)}
        />
      </div>
  );
};
