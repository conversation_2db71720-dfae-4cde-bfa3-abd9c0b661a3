'use client';

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Virtuoso } from 'react-virtuoso';
// DragDropContext is now provided at the app level
import { cn } from '@/lib/utils';
import { ConfigurableTaskCard } from './cards/ConfigurableTaskCard';
import { CardLayoutConfigModal } from './cards/CardLayoutConfigModal';
import { useCardPerformance } from './cards/useCardPerformance';
import { Button } from '@/components/ui/button';
import { Settings2, LayoutGrid, Grid3X3, Activity } from 'lucide-react';
import type { Task, Vehicle, TaskListStoredSettings, VehicleDisplayMode } from '@/types';

// 默认布局配置
const DEFAULT_LAYOUT_CONFIG = {
  topFields: [
    { id: 'taskNumber', label: '任务编号', visible: true, order: 0 },
    { id: 'constructionSite', label: '施工地点', visible: true, order: 1 },
    { id: 'dispatchStatus', label: '发车状态', visible: true, order: 2 },
    { id: 'customerName', label: '客户名称', visible: true, order: 3 },
  ],
  middleFields: [
    { id: 'requiredVolume', label: '需求方量', visible: true, order: 0 },
    { id: 'completedVolume', label: '完成方量', visible: true, order: 1 },
    { id: 'progress', label: '完成进度', visible: true, order: 2 },
    { id: 'scheduledTime', label: '计划时间', visible: true, order: 3 },
    { id: 'estimatedDuration', label: '预计时长', visible: true, order: 4 },
    { id: 'contactPhone', label: '联系电话', visible: false, order: 5 },
  ],
  showVehicleArea: true,
  cardSize: 'medium' ,
  spacing: 'normal' ,
  theme: 'default' ,
};

interface ConfigurableTaskCardViewProps {
  filteredTasks: Task[];
  vehicles: Vehicle[];
  settings: TaskListStoredSettings;
  vehicleDisplayMode: VehicleDisplayMode;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onOpenStyleEditor: () => void;
  onOpenDeliveryOrderDetailsForVehicle: (vehicleId: string, taskId: string) => void;
  onOpenVehicleCardContextMenu: (e: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onTaskContextMenu: (e: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick: (task: Task) => void;
  onVehicleDispatchedToLine?: (vehicleId: string, lineId: string, taskId: string) => void;
}

export const ConfigurableTaskCardView: React.FC<ConfigurableTaskCardViewProps> = ({
  filteredTasks,
  vehicles,
  settings,
  vehicleDisplayMode,
  onCancelVehicleDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetailsForVehicle,
  onOpenVehicleCardContextMenu,
  onTaskContextMenu,
  onTaskDoubleClick,
  onVehicleDispatchedToLine,
}) => {
  const [layoutConfig, setLayoutConfig] = useState(DEFAULT_LAYOUT_CONFIG);
  const [configModalOpen, setConfigModalOpen] = useState(false);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1280);

  // 性能监控
  const {
    performanceConfig,
    shouldUseVirtualScroll
  } = useCardPerformance({
    componentName: 'ConfigurableTaskCardView',
    config: { virtualScroll: { enabled: true, overscan: 50, increaseViewportBy: 200, itemHeight: 200 } },
    enableMonitoring: true
  });

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 计算网格列数
  const columnsPerRow = useMemo(() => {
    const width = windowWidth;
    
    switch (layoutConfig.cardSize) {
      case 'medium':
        if (width >= 1536) return 5; // 2xl
        if (width >= 1280) return 4; // xl
        if (width >= 1024) return 3; // lg
        if (width >= 768) return 2;  // md
        return 1;
      case 'normal':
        if (width >= 1536) return 3; // 2xl
        if (width >= 1280) return 2; // xl
        if (width >= 1024) return 2; // lg
        return 1;
      default: // medium
        if (width >= 1536) return 4; // 2xl
        if (width >= 1280) return 3; // xl
        if (width >= 1024) return 2; // lg
        if (width >= 768) return 2;  // md
        return 1;
    }
  }, [windowWidth, layoutConfig.cardSize]);

  // 网格样式
  const gridColumns = useMemo(() => {
    return `grid-cols-1 md:grid-cols-${Math.min(columnsPerRow, 2)} lg:grid-cols-${Math.min(columnsPerRow, 3)} xl:grid-cols-${columnsPerRow}`;
  }, [columnsPerRow]);

  // 间距样式
  const spacingClass = useMemo(() => {
    switch (layoutConfig.spacing) {
      case 'compact': return 'gap-1 p-1';

      case 'loose': return 'gap-3 p-3';
      default: return 'gap-2 p-2';
    }
  }, [layoutConfig.spacing]);

  // 缓存车辆分组
  const vehiclesByTask = useMemo(() => {
    const map = new Map<string, Vehicle[]>();
    vehicles.forEach(vehicle => {
      if (vehicle.assignedTaskId) {
        if (!map.has(vehicle.assignedTaskId)) {
          map.set(vehicle.assignedTaskId, []);
        }
        map.get(vehicle.assignedTaskId)!.push(vehicle);
      }
    });
    return map;
  }, [vehicles]);

  // 是否使用虚拟滚动
  const useVirtualScroll = shouldUseVirtualScroll(filteredTasks.length);

  // 计算总行数（用于虚拟滚动）
  const totalRows = Math.ceil(filteredTasks.length / columnsPerRow);

  // 渲染行（虚拟滚动）
  const renderRow = useCallback((index: number) => {
    const startIndex = index * columnsPerRow;
    const endIndex = Math.min(startIndex + columnsPerRow, filteredTasks.length);
    const rowTasks = filteredTasks.slice(startIndex, endIndex);

    return (
      <div
        key={`row-${index}`}
        className={cn(
          "grid w-full",
          gridColumns,
          spacingClass,
          "virtual-row-performance"
        )}
      >
        {rowTasks.map((task) => {
          const taskVehicles = vehiclesByTask.get(task.id) || [];
          
          return (
            <ConfigurableTaskCard
              key={task.id}
              task={task}
              vehicles={taskVehicles}
              layoutConfig={layoutConfig}
              vehicleDisplayMode={vehicleDisplayMode}
              inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
              density={settings.density}
              productionLineCount={task.productionLineCount || 1}
              onCancelDispatch={onCancelVehicleDispatch}
              onOpenStyleEditor={onOpenStyleEditor}
              onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetailsForVehicle}
              onOpenContextMenu={onOpenVehicleCardContextMenu}
              onTaskContextMenu={onTaskContextMenu}
              onTaskDoubleClick={onTaskDoubleClick}
            />
          );
        })}
      </div>
    );
  }, [
    columnsPerRow,
    filteredTasks,
    gridColumns,
    spacingClass,
    layoutConfig,
    vehiclesByTask,
    vehicleDisplayMode,
    settings.inTaskVehicleCardStyles,
    settings.density,
    onCancelVehicleDispatch,
    onOpenStyleEditor,
    onOpenDeliveryOrderDetailsForVehicle,
    onOpenVehicleCardContextMenu,
    onTaskContextMenu,
    onTaskDoubleClick,
  ]);

  // 拖拽处理现在由 DragDropContext 在应用级别处理

  // 保存配置
  const handleSaveConfig = useCallback((config: typeof layoutConfig) => {
    setLayoutConfig(config);
    setConfigModalOpen(false);
    
    // 这里可以保存到本地存储或发送到服务器
    localStorage.setItem('taskCardLayoutConfig', JSON.stringify(config));
  }, []);

  // 加载保存的配置
  useEffect(() => {
    const savedConfig = localStorage.getItem('taskCardLayoutConfig');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        setLayoutConfig({ ...DEFAULT_LAYOUT_CONFIG, ...parsed });
      } catch (error) {
        console.error('Failed to parse saved layout config:', error);
      }
    }
  }, []);

  function getPerformanceReport(): any {
    throw new Error('Function not implemented.');
  }

  return (
    <div className="flex flex-col h-full">
        {/* 工具栏 */}
        <div className="flex items-center justify-between  border-b bg-background/95 backdrop-blur">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <LayoutGrid className="w-5 h-5 text-muted-foreground" />
              <span className="font-medium">
                可配置卡片 ({filteredTasks.length})
                {process.env.NODE_ENV === 'development' && (
                  <span className="text-xs text-muted-foreground ml-2">
                    {useVirtualScroll ? '(虚拟滚动)' : '(标准渲染)'}
                  </span>
                )}
              </span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setConfigModalOpen(true)}
              className="flex items-center gap-2"
            >
              <Settings2 className="w-4 h-4" />
              布局配置
            </Button>

            {process.env.NODE_ENV === 'development' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => console.log(getPerformanceReport())}
                className="flex items-center gap-2"
              >
                <Activity className="w-4 h-4" />
                性能报告
              </Button>
            )}
          </div>
        </div>

        {/* 卡片内容区域 */}
        <div className="flex-1 overflow-hidden">
          {useVirtualScroll ? (
            <Virtuoso
              totalCount={totalRows}
              itemContent={renderRow}
              overscan={performanceConfig.virtualScroll.overscan}
              increaseViewportBy={performanceConfig.virtualScroll.increaseViewportBy}
              className="card-grid-performance"
            />
          ) : (
            <div className={cn("overflow-auto h-full card-grid-performance", spacingClass)}>
              <div className={cn("grid", gridColumns, spacingClass)}>
                {filteredTasks.map((task) => {
                  const taskVehicles = vehiclesByTask.get(task.id) || [];
                  
                  return (
                    <ConfigurableTaskCard
                      key={task.id}
                      task={task}
                      vehicles={taskVehicles}
                      layoutConfig={layoutConfig}
                      vehicleDisplayMode={vehicleDisplayMode}
                      inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
                      density={settings.density}
                      productionLineCount={task.productionLineCount || 1}
                      onCancelDispatch={onCancelVehicleDispatch}
                      onOpenStyleEditor={onOpenStyleEditor}
                      onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetailsForVehicle}
                      onOpenContextMenu={onOpenVehicleCardContextMenu}
                      onTaskContextMenu={onTaskContextMenu}
                      onTaskDoubleClick={onTaskDoubleClick}
                    />
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* 配置弹窗 */}
        <CardLayoutConfigModal
          open={configModalOpen}
          onOpenChange={setConfigModalOpen}
          config={layoutConfig}
          onConfigChange={setLayoutConfig}
          onSave={handleSaveConfig}
          onCancel={() => setConfigModalOpen(false)}
        />
      </div>
  );
};
