// src/components/sections/task-list/task-list.tsx
'use client';

import React, { useCallback, useState, useEffect, useRef } from 'react';
import { useDrop } from 'react-dnd';
import type { ColumnSizingState, OnChangeFn, VisibilityState, ColumnOrderState } from '@tanstack/react-table';
import type {
  CustomColumnDefinition, Task, TaskGroupConfig} from '@/types';
import {
  MoreVertical, Settings, Settings2, Columns, FileUp, FileDown, RotateCcw, Rows, LayoutGrid, Palette, BarChart3,
  Grid3X3, ChevronDown, CheckCircle2
} from 'lucide-react';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuRadioGroup, DropdownMenuRadioItem, DropdownMenuSub, DropdownMenuSubContent, DropdownMenuSubTrigger, DropdownMenuPortal, DropdownMenuTrigger, DropdownMenuCheckboxItem
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useUiStore } from '@/store/uiStore';
import { useTaskListSettings } from '@/hooks/useTaskListSettings';
import { useTaskContextMenu } from '@/hooks/useTaskContextMenu';
import { useVehicleCardContextMenu } from '@/hooks/useVehicleCardContextMenu';
import { useQRCodeModal } from '@/hooks/useQRCodeModal';
import { useTaskAbbreviationModal } from '@/hooks/useTaskAbbreviationModal';
import { MixRatioPageV2 } from '@/components/pages/mix-ratio-page-v2';
import { useCurrentPlantInfo } from '@/hooks/useCurrentPlantInfo';
import { ALL_TASK_COLUMNS_CONFIG } from './task-list.config';
import { getCardGridClasses } from './task-list-density-config';
import {
  getTaskColumnId,
  getTaskListDisplayMode,
  getTaskListDensityMode,
  getVehicleDisplayMode,
  getTask,
  convertColumnTextStyles
} from './task-list-type-guards';
import { ItemTypes } from '@/constants/dndItemTypes';
import { useToast } from '@/hooks/use-toast';
import { SectionContainer } from '@/components/shared/section-container';
import { useDraggableFab } from '@/hooks/useDraggableFab';
import { TaskRowHighlightProvider } from '@/contexts/TaskRowHighlightContext';
import { useTaskCardConfigManager } from './components/task-card-config-manager';
import { useTaskListDragHandlers } from './components/task-list-event-handlers';
import { TaskListFAB } from './components/task-list-fab';
import { TaskListContentRenderer } from './components/task-list-content-renderer';
import { TaskListModalManager } from './components/task-list-modal-manager';

// 新的专门Hook
import { useTaskListData } from './hooks/use-task-list-data';
import { useTaskListColumns } from './hooks/use-task-list-columns';
import { useTaskListStyles } from './hooks/use-task-list-styles';
import { useTaskListBusinessLogic } from './hooks/use-task-list-business-logic';
import {MixRatioPage} from "@/components/pages/mix-ratio-page";
export interface TaskListProps {
  productionLineCount: number;
}

const FAB_SIZE = 40;
const FAB_MARGIN = 16;

export function TaskList() {
  // 使用新的专门Hook
  const taskListData = useTaskListData();
  const taskListBusinessLogic = useTaskListBusinessLogic();

  const { selectedPlantId, taskStatusFilter, vehicleDisplayMode, setVehicleDisplayMode } = useUiStore();
  const { productionLineCount, isLoadingPlants: isLoadingPlantInfo } = useCurrentPlantInfo();
  const { toast } = useToast();

  const {
    settings, updateSetting, handleColumnVisibilityChange, handleColumnOrderChange,
    handleColumnTextStyleChange, handleColumnBackgroundChange,
    resetAllSettings, exportSettings, triggerImport, fileInputRef, isSettingsLoaded,
    isColumnVisibilityModalOpen, isColumnSpecificStyleModalOpen, editingColumnDef,
    openColumnVisibilityModal, closeColumnVisibilityModal, openColumnSpecificStyleModal, closeColumnSpecificStyleModal,
    isStyleEditorModalOpen, openStyleEditorModal, closeStyleEditorModal, handleVehiclesPerRowChange,
    handleToggleGroupCollapse,
    isGroupConfigModalOpen,
    setIsGroupConfigModalOpen,
  } = useTaskListSettings();
  const taskListStyles = useTaskListStyles(settings);

  const {
    displayMode, density, enableZebraStriping
    ,
  } = settings;

  const {
    isTaskContextMenuOpen, taskContextMenuPosition, contextMenuTaskData, openTaskContextMenu, closeTaskContextMenu,
    isTankerNoteModalOpen, selectedTaskForTankerNote, openTankerNoteModal, closeTankerNoteModal,
    isReminderConfigModalOpen, selectedTaskForReminderConfig, openReminderConfigModal, closeReminderConfigModal,
  } = useTaskContextMenu();

  const {
    isContextMenuOpen: isVehicleCardContextMenuOpen, contextMenuPosition: vehicleCardContextMenuPosition,
    contextMenuData: vehicleCardContextMenuContext, openContextMenu: openVehicleCardContextMenu,
    closeContextMenu: closeVehicleCardContextMenu, isDeliveryOrderDetailsModalOpen,
    selectedVehicleForDeliveryOrder, selectedTaskForDeliveryOrder, openDeliveryOrderDetailsModal, closeDeliveryOrderDetailsModal,
  } = useVehicleCardContextMenu();

  const {
    isQRCodeModalOpen, selectedTaskForQRCode, openQRCodeModal, closeQRCodeModal,
  } = useQRCodeModal();

  const {
    isTaskAbbreviationModalOpen, selectedTaskForAbbreviation, openTaskAbbreviationModal,
    closeTaskAbbreviationModal, handleSaveAbbreviation,
  } = useTaskAbbreviationModal();

  // 配比页面状态管理
  const [isMixRatioPageOpen, setIsMixRatioPageOpen] = useState(false);
  const [selectedTaskForMixRatio, setSelectedTaskForMixRatio] = useState<Task | null>(null);

  const openMixRatioPage = useCallback((task: Task) => {
    setSelectedTaskForMixRatio(task);
    setIsMixRatioPageOpen(true);
  }, []);

  const closeMixRatioPage = useCallback(() => {
    setIsMixRatioPageOpen(false);
    setSelectedTaskForMixRatio(null);
  }, []);

  // const fabPosition = useMemo(() => ({ x: FAB_MARGIN, y: FAB_MARGIN }), []);
  // const fabRef = useRef<HTMLButtonElement>(null);


  const taskListBoundaryRef = useRef<HTMLDivElement>(null);
  const {
    position: fabPosition,
    isDragging: isFabDragging,
    fabRef,
    handleContextMenu: handleFabContextMenu
  } = useDraggableFab({
    initialPosition: { x: FAB_MARGIN, y: FAB_MARGIN },
    containerRef: taskListBoundaryRef,
    fabSize: FAB_SIZE,
    margin: FAB_MARGIN,
    storageKey: 'task-list-fab-position'
  });

  const didShowResizeHintRef = useRef(false);

  const handleOpenGroupConfig = useCallback(() => {
    setIsGroupConfigModalOpen(true);
  }, [setIsGroupConfigModalOpen]);

  const handleUpdateGroupConfig = useCallback((config: Partial<TaskGroupConfig>) => {
    updateSetting('groupConfig', {
      ...settings.groupConfig,
      ...config
    });
  }, [updateSetting, settings.groupConfig]);

  // 深度合并配置的辅助函数

  // 任务卡片配置管理
  const {
    taskCardConfig,
    handleTaskCardConfigChange,
  } = useTaskCardConfigManager();

  // 事件处理器
  const dragHandlers = useTaskListDragHandlers();

  // 卡片视图拖拽状态
  const [dragOverTaskId, setDragOverTaskId] = useState<string | null>(null);
  const [dragOverProductionLineId, setDragOverProductionLineId] = useState<string | null>(null);




  const [{ isOverTaskList, canDropOnTaskList }] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: dragHandlers.handleTaskListDrop,
    collect: (monitor) => ({
      isOverTaskList: monitor.isOver({ shallow: true }),
      canDropOnTaskList: monitor.canDrop(),
    }),
  });


  // 使用新的表格列配置Hook
  const tableColumns = useTaskListColumns({
    densityStyles: taskListStyles.currentDensityStyles,
    vehicleDisplayMode,
    inTaskVehicleCardStyles: settings.inTaskVehicleCardStyles,
    productionLineCount,
    onDropOnProductionLine: taskListBusinessLogic.handleDropOnProductionLine,
    onCancelVehicleDispatch: taskListBusinessLogic.handleCancelVehicleDispatch,
    onOpenStyleEditor: openStyleEditorModal,
    onOpenDeliveryOrderDetails: openDeliveryOrderDetailsModal,
    onOpenVehicleCardContextMenu: openVehicleCardContextMenu,
    getColumnBackgroundProps: taskListStyles.getColumnBackgroundProps,
    settings, // 传递完整的settings对象
  });


  const handleHeaderDoubleClick = useCallback((event: React.MouseEvent, columnDef: CustomColumnDefinition) => {
    event.preventDefault(); event.stopPropagation();
    if (displayMode === 'table') {
      if (columnDef.id === 'dispatchedVehicles' || columnDef.id === 'productionLines') {
        openColumnSpecificStyleModal(columnDef);
      } else if (columnDef.isStyleable !== false) {
        openColumnSpecificStyleModal(columnDef);
      } else {
        toast({ title: "样式设置", description: `列 \"${columnDef.label}\" 不支持单独的样式设置。`, variant: "default" });
      }
    }
  }, [displayMode, openColumnSpecificStyleModal, toast]);

  const handleHeaderContextMenu = useCallback((event: React.MouseEvent, columnDef: CustomColumnDefinition) => {
    event.preventDefault();
    const columnId = getTaskColumnId(columnDef.id);

    // 检查该列是否允许分组
    const isAllowed = settings.groupConfig.allowedGroupColumns.includes(columnId);
    const isDisallowed = settings.groupConfig.disallowedGroupColumns.includes(columnId);

    if (isDisallowed || !isAllowed) {
      toast({
        title: "分组限制",
        description: `列 \"${columnDef.label}\" 不支持分组操作。`,
        variant: "default"
      });
      return;
    }

    // 切换分组
    const newGroupBy = settings.groupConfig.groupBy === columnId ? 'none' : columnId;
    const newEnabled = newGroupBy !== 'none';

    updateSetting('groupConfig', {
      ...settings.groupConfig,
      groupBy: newGroupBy,
      enabled: newEnabled
    });

    toast({
      title: newEnabled ? "启用分组" : "取消分组",
      description: newEnabled ? `已按 \"${columnDef.label}\" 分组` : "已取消分组",
      variant: "default"
    });
  }, [settings.groupConfig, updateSetting, toast]);

  /**
   * 处理取消分组操作
   */
  const handleCancelGrouping = useCallback(() => {
    updateSetting('groupConfig', {
      ...settings.groupConfig,
      groupBy: 'none',
      enabled: false
    });

    toast({
      title: "取消分组",
      description: "已取消任务分组",
      variant: "default"
    });
  }, [settings.groupConfig, updateSetting, toast]);

  // 卡片配置状态
  const [cardConfig, setCardConfig] = useState({
    size: 'medium' as 'small' | 'medium' | 'large',
    layout: 'standard' as 'compact' | 'standard' | 'detailed' | 'minimal',
    theme: 'default' as 'default' | 'modern' | 'glass' | 'gradient' | 'dark',
    spacing: 'normal' as 'tight' | 'normal' | 'loose',
    borderRadius: 'medium' as 'none' | 'small' | 'medium' | 'large' | 'full',
    shadow: 'medium' as 'none' | 'small' | 'medium' | 'large' | 'glow',
    animation: 'smooth' as 'none' | 'subtle' | 'smooth' | 'bouncy',
    columns: 'auto' as 'auto' | '1' | '2' | '3' | '4' | '5' | '6',
  });

  const [taskCardConfigModalOpen, setTaskCardConfigModalOpen] = useState(false);
  // 获取列图标
  const getColumnIcon = (columnId: string): string => {
    const iconMap: Record<string, string> = {
      'projectName': '🏗️',
      'strength': '💪',
      'pouringMethod': '🔧',
      'supplyDate': '📅',
      'pumpTruck': '🚛',
      'constructionUnit': '🏢',
      'constructionSite': '📍',
      'dispatchStatus': '🔄',
      'deliveryStatus': '🚚',
      'plantId': '🏭',
    };
    return iconMap[columnId] || '📋';
  };

  /**
   * 渲染卡片模式操作栏
   */
  const renderCardModeActions = () => {
    if (displayMode !== 'card') return null;
    return (
      <div className="flex items-center gap-2">
        {/* 任务数量显示 */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <LayoutGrid className="w-4 h-4" />
          <span>{taskListData.filteredTasks.length} 个任务</span>
        </div>
        {/* 分组按钮 - 改进版 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant={settings.groupConfig.enabled ? "default" : "outline"}
              size="sm"
              className="h-6 gap-1"
              title={settings.groupConfig.enabled ? '分组设置' : '启用分组'}
            >
              <Grid3X3 className="w-3 h-3" />
              {settings.groupConfig.enabled && <ChevronDown className="w-2 h-2" />}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel>分组设置</DropdownMenuLabel>
            <DropdownMenuSeparator />

            {/* 取消分组选项 */}
            <DropdownMenuItem
              onClick={handleCancelGrouping}
              className={!settings.groupConfig.enabled ? 'bg-accent' : ''}
            >
              <div className="flex items-center gap-2">
                <span>📋</span>
                <span>不分组</span>
              </div>
            </DropdownMenuItem>

            <DropdownMenuSeparator />
            <DropdownMenuLabel>按列分组</DropdownMenuLabel>

            {/* 可分组的列选项 */}
            {settings.groupConfig.allowedGroupColumns.map((columnId) => {
              const columnDef = ALL_TASK_COLUMNS_CONFIG.find(col => col.id === columnId);
              if (!columnDef) return null;

              const isSelected = settings.groupConfig.groupBy === columnId;

              return (
                <DropdownMenuItem
                  key={columnId}
                  onClick={() => {
                    updateSetting('groupConfig', {
                      ...settings.groupConfig,
                      groupBy: columnId,
                      enabled: true
                    });
                    toast({
                      title: "启用分组",
                      description: `已按 "${columnDef.label}" 分组`,
                      variant: "default"
                    });
                  }}
                  className={isSelected ? 'bg-accent' : ''}
                >
                  <div className="flex items-center gap-2">
                    <span>{getColumnIcon(columnId)}</span>
                    <span>{columnDef.label}</span>
                    {isSelected && <CheckCircle2 className="w-3 h-3 ml-auto" />}
                  </div>
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* 新的任务卡片配置按钮 */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setTaskCardConfigModalOpen(true)}
          className="h-6"
          title="任务卡片配置"
        >
          <Settings className="w-3 h-3" />
        </Button>


      </div>
    );
  };

  /**
   * 渲染header操作按钮
   */
  const renderHeaderActions = () => {
    if (!isSettingsLoaded) return null;

    return (
      <div className="flex items-center gap-2">
        {/* 卡片模式操作栏 */}
        {renderCardModeActions()}

        {/* 通用设置下拉菜单 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-6">
              <MoreVertical className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <Settings2 className="mr-2 h-3.5 w-3.5" />视图设置
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  <DropdownMenuLabel>显示模式</DropdownMenuLabel>
                  <DropdownMenuRadioGroup value={displayMode} onValueChange={(value) => updateSetting('displayMode', getTaskListDisplayMode(value))}>
                    <DropdownMenuRadioItem value="table"><Rows className="mr-2 h-3.5 w-3.5" />表格</DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="card"><LayoutGrid className="mr-2 h-3.5 w-3.5" />卡片</DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>密度</DropdownMenuLabel>
                  <DropdownMenuRadioGroup value={density} onValueChange={(value) => updateSetting('density', getTaskListDensityMode(value))}>
                    <DropdownMenuRadioItem value="compact">紧凑</DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="normal">标准</DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="loose">宽松</DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                  <DropdownMenuSeparator />
                  <DropdownMenuCheckboxItem checked={enableZebraStriping} onCheckedChange={(checked) => updateSetting('enableZebraStriping', !!checked)}>斑马条纹</DropdownMenuCheckboxItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>车辆显示</DropdownMenuLabel>
                  <DropdownMenuRadioGroup value={vehicleDisplayMode} onValueChange={(value) => setVehicleDisplayMode(getVehicleDisplayMode(value))}>
                    <DropdownMenuRadioItem value="licensePlate">车牌号</DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="internalId">内部编号</DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
            <DropdownMenuItem onClick={() => openColumnVisibilityModal()}><Columns className="mr-2 h-3.5 w-3.5" />列显示与排序</DropdownMenuItem>
            <DropdownMenuItem onClick={handleOpenGroupConfig}><BarChart3 className="mr-2 h-3.5 w-3.5" />分组设置</DropdownMenuItem>
            <DropdownMenuItem onClick={() => openStyleEditorModal()}><Palette className="mr-2 h-3.5 w-3.5" />车卡样式</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={triggerImport}><FileUp className="mr-2 h-3.5 w-3.5" />导入样式</DropdownMenuItem>
            <DropdownMenuItem onClick={exportSettings}><FileDown className="mr-2 h-3.5 w-3.5" />导出样式</DropdownMenuItem>
            <DropdownMenuItem onClick={resetAllSettings}><RotateCcw className="mr-2 h-3.5 w-3.5" />恢复默认</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  };
  const fabComponent = (
    <TaskListFAB
      fabPosition={fabPosition}
      isFabDragging={isFabDragging}
      fabSize={FAB_SIZE}
      displayMode={displayMode}
      density={density}
      enableZebraStriping={enableZebraStriping}
      vehicleDisplayMode={vehicleDisplayMode}
      isSettingsLoaded={isSettingsLoaded}
      onDisplayModeChange={(mode) => updateSetting('displayMode', mode)}
      onDensityChange={(density) => updateSetting('density', density)}
      onZebraStripingChange={(enabled) => updateSetting('enableZebraStriping', enabled)}
      onVehicleDisplayModeChange={setVehicleDisplayMode}
      onOpenColumnVisibilityModal={openColumnVisibilityModal}
      onOpenGroupConfig={handleOpenGroupConfig}
      onOpenStyleEditorModal={openStyleEditorModal}
      onTriggerImport={triggerImport}
      onExportSettings={exportSettings}
      onResetAllSettings={resetAllSettings}
      onFabContextMenu={handleFabContextMenu}
    />
  );

  useEffect(() => {
    if (isSettingsLoaded && taskListBoundaryRef.current && fabRef.current) {
      const containerRect = taskListBoundaryRef.current.getBoundingClientRect();
      const fabCurrentRef = fabRef.current;
      if (containerRect.width > 0 && containerRect.height > 0 && fabCurrentRef) {
      }
    }
  }, [isSettingsLoaded, displayMode, taskListData.filteredTasks.length, fabPosition]);

  useEffect(() => {
    if (isSettingsLoaded && !didShowResizeHintRef.current && displayMode === 'table') {
      didShowResizeHintRef.current = true;
      toast({
        title: "列宽调整提示",
        description: "您可以通过拖动列标题右侧边缘来调整列宽。",
        duration: 5000,
      });
    }
  }, [isSettingsLoaded, displayMode, toast]);

  const handleColumnSizingChange: OnChangeFn<ColumnSizingState> = (updater) => {
    const newSizing = typeof updater === 'function' ? updater(settings.columnWidths) : updater;
    updateSetting('columnWidths', newSizing);
  };

  const handleColumnVisibilityChangeForTable: OnChangeFn<VisibilityState> = (updater) => {
    const newVisibility = typeof updater === 'function' ? updater(settings.columnVisibility) : updater;
    updateSetting('columnVisibility', newVisibility);
  };

  const renderContent = () => {
    if (!isSettingsLoaded || isLoadingPlantInfo) {
      return <div className="h-full flex items-center justify-center"><p>加载中...</p></div>;
    }
    if (!selectedPlantId && taskListData.filteredTasks.length === 0) {
      return <div className="flex items-center justify-center h-full text-muted-foreground p-4">请先选择一个搅拌站。</div>;
    }
    if (selectedPlantId && taskListData.filteredTasks.length === 0) {
      return <div className="flex items-center justify-center h-full text-muted-foreground p-4">此搅拌站当前状态无任务。</div>;
    }

    return (
      <TaskListContentRenderer
        displayMode={displayMode}
        filteredTasks={taskListData.filteredTasks}
        tasksWithVehicles={taskListData.expandedTasks}
        taskGroups={taskListData.taskGroups}
        allVehicles={taskListData.allVehicles}
        tableColumns={tableColumns?.tableColumns ?? []}
        settings={settings}
        densityStyles={taskListStyles.currentDensityStyles}
        vehicleDisplayMode={vehicleDisplayMode}
        taskStatusFilter={taskStatusFilter}
        taskCardConfig={taskCardConfig}
        tableTotalWidth={taskListStyles.tableTotalWidth}
        estimateRowHeight={taskListStyles.estimateRowHeight}
        cardGridContainerClasses={taskListStyles.cardGridContainerClasses}
        dragOverTaskId={dragOverTaskId}
        setDragOverTaskId={setDragOverTaskId}
        dragOverProductionLineId={dragOverProductionLineId}
        setDragOverProductionLineId={setDragOverProductionLineId}
        onColumnSizingChange={handleColumnSizingChange}
        onColumnOrderChange={(updaterOrValue: ColumnOrderState | ((old: ColumnOrderState) => ColumnOrderState)) => {
          const newOrder = typeof updaterOrValue === 'function'
            ? updaterOrValue(settings.columnOrder)
            : updaterOrValue;
          handleColumnOrderChange(newOrder);
        }}
        onColumnVisibilityChange={handleColumnVisibilityChangeForTable}
        onHeaderContextMenu={handleHeaderContextMenu}
        onHeaderDoubleClick={handleHeaderDoubleClick}
        onRowContextMenu={(e, row) => {
          const task = getTask(row.original);
          if (task) openTaskContextMenu(e, task.id);
        }}
        onRowDoubleClick={(row) => {
          const task = getTask(row.original);
          if (task) openTankerNoteModal(task);
        }}
        onDropOnProductionLine={taskListBusinessLogic.handleDropOnProductionLine}
        onToggleGroupCollapse={handleToggleGroupCollapse}
        onCancelGrouping={handleCancelGrouping}
        onTaskContextMenu={(e, task) => openTaskContextMenu(e, task.id)}
        onTaskDoubleClick={openTankerNoteModal}
        onVehicleDrop={(vehicle, taskId) => taskListBusinessLogic.handleDropOnProductionLine(vehicle, taskId, 'L1')}
        onOpenVehicleCardContextMenu={openVehicleCardContextMenu}
        onOpenDeliveryOrderDetailsForVehicle={(vId, tId) => openDeliveryOrderDetailsModal(taskListData.getVehicleById(vId)!, taskListData.getTaskById(tId)!)}
        onOpenStyleEditor={openStyleEditorModal}
        onCancelVehicleDispatch={taskListBusinessLogic.handleCancelVehicleDispatch}
        onVehicleDispatchedToLine={taskListBusinessLogic.handleDropOnProductionLine}
        onDropVehicleFromPanelOnTaskCard={(vehicle, taskId) => taskListBusinessLogic.handleDropOnProductionLine(vehicle, taskId, 'L1')}
        onTaskCardConfigChange={handleTaskCardConfigChange}
        onOpenCardConfigModal={() => setTaskCardConfigModalOpen(true)}
        getColumnBackgroundProps={taskListStyles.getColumnBackgroundProps}
        getStatusLabelProps={(status: string) => ({
          label: taskListStyles.getStatusLabelProps(status as "ReadyToProduce" | "RatioSet" | "InProgress" | "Paused" | "Completed" | "Cancelled" | "New").label,
          variant: taskListStyles.getStatusLabelProps(status as "ReadyToProduce" | "RatioSet" | "InProgress" | "Paused" | "Completed" | "Cancelled" | "New").className
        })}
      />
    );
  };

  const cardGridContainerClasses = getCardGridClasses(density);


  return (
    <TaskRowHighlightProvider>
      <div className="h-full"
      >
        <SectionContainer
          title="任务列表"
          fabRender={fabComponent}
          hideHeader={displayMode === 'table'}
          contentClassName={cn(
            displayMode !== 'table'
              ? cardGridContainerClasses
              : 'p-0 h-full',
            isOverTaskList && canDropOnTaskList && "bg-primary/10 ring-2 ring-primary"
          )}
          containerRef={taskListBoundaryRef}
          actionButtons={renderHeaderActions()}
        >
          {renderContent()}
          <TaskListModalManager
            isTankerNoteModalOpen={isTankerNoteModalOpen}
            closeTankerNoteModal={closeTankerNoteModal}
            selectedTaskForTankerNote={selectedTaskForTankerNote}
            isColumnVisibilityModalOpen={isColumnVisibilityModalOpen}
            closeColumnVisibilityModal={closeColumnVisibilityModal}
            openColumnVisibilityModal={openColumnVisibilityModal}
            allColumns={ALL_TASK_COLUMNS_CONFIG}
            columnVisibility={settings.columnVisibility}
            handleColumnVisibilityChange={handleColumnVisibilityChange}
            currentOrder={settings.columnOrder}
            handleColumnOrderChange={(newOrder) => updateSetting('columnOrder', newOrder)}
            isColumnSpecificStyleModalOpen={isColumnSpecificStyleModalOpen}
            closeColumnSpecificStyleModal={closeColumnSpecificStyleModal}
            editingColumnDef={editingColumnDef}
            columnTextStyles={convertColumnTextStyles(settings.columnTextStyles)}
            columnBackgrounds={settings.columnBackgrounds}
            handleColumnTextStyleChange={handleColumnTextStyleChange}
            handleColumnBackgroundChange={handleColumnBackgroundChange}
            isStyleEditorModalOpen={isStyleEditorModalOpen}
            closeStyleEditorModal={closeStyleEditorModal}
            inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
            updateSetting={updateSetting}
            isDeliveryOrderDetailsModalOpen={isDeliveryOrderDetailsModalOpen}
            closeDeliveryOrderDetailsModal={closeDeliveryOrderDetailsModal}
            selectedVehicleForDeliveryOrder={selectedVehicleForDeliveryOrder}
            selectedTaskForDeliveryOrder={selectedTaskForDeliveryOrder}
            isReminderConfigModalOpen={isReminderConfigModalOpen}
            closeReminderConfigModal={closeReminderConfigModal}
            selectedTaskForReminderConfig={selectedTaskForReminderConfig}
            isQRCodeModalOpen={isQRCodeModalOpen}
            closeQRCodeModal={closeQRCodeModal}
            selectedTaskForQRCode={selectedTaskForQRCode}
            isTaskAbbreviationModalOpen={isTaskAbbreviationModalOpen}
            closeTaskAbbreviationModal={closeTaskAbbreviationModal}
            selectedTaskForAbbreviation={selectedTaskForAbbreviation}
            handleSaveAbbreviation={handleSaveAbbreviation}
            taskCardConfigModalOpen={taskCardConfigModalOpen}
            setTaskCardConfigModalOpen={setTaskCardConfigModalOpen}
            taskCardConfig={taskCardConfig}
            handleTaskCardConfigChange={handleTaskCardConfigChange}
            isGroupConfigModalOpen={isGroupConfigModalOpen}
            setIsGroupConfigModalOpen={setIsGroupConfigModalOpen}
            groupConfig={settings.groupConfig}
            handleOpenGroupConfig={handleUpdateGroupConfig}
            isTaskContextMenuOpen={isTaskContextMenuOpen}
            taskContextMenuPosition={taskContextMenuPosition}
            contextMenuTaskData={contextMenuTaskData}
            closeTaskContextMenu={closeTaskContextMenu}
            openTankerNoteModal={openTankerNoteModal}
            openReminderConfigModal={openReminderConfigModal}
            openQRCodeModal={openQRCodeModal}
            openTaskAbbreviationModal={openTaskAbbreviationModal}
            openMixRatioPage={openMixRatioPage}
            filteredTasks={taskListData.filteredTasks}
            currentDisplayMode={displayMode}
            onSetDisplayMode={(mode) => updateSetting('displayMode', mode)}
            isVehicleCardContextMenuOpen={isVehicleCardContextMenuOpen}
            vehicleCardContextMenuPosition={vehicleCardContextMenuPosition}
            vehicleCardContextMenuContext={vehicleCardContextMenuContext}
            closeVehicleCardContextMenu={closeVehicleCardContextMenu}
            onVehiclesPerRowChange={handleVehiclesPerRowChange}
          />
          <input type="file" accept=".json" ref={fileInputRef} onChange={(e) => { if (e.target.files && e.target.files.length > 0) { settings.handleImportFile(e) } }} style={{ display: 'none' }} />
        </SectionContainer>
      </div>

      {/* 配比页面 */}
      {isMixRatioPageOpen && selectedTaskForMixRatio && (
        <ModernMixRatioPage
          task={selectedTaskForMixRatio}
          onClose={closeMixRatioPage}
        />
      )}
    </TaskRowHighlightProvider>
  );
}
