import type { Task, TaskGroup, TaskGroupConfig, TaskGroupStats, TaskColumnId } from '@/types';

/**
 * 任务分组工具函数
 * 提供高效的任务分组和统计功能
 */

/**
 * 获取任务的分组键值
 */
export function getTaskGroupKey(task: Task, groupBy: TaskGroupConfig['groupBy']): string {
  switch (groupBy) {
    case 'dispatchStatus' as TaskColumnId:
      return task.dispatchStatus || '未分派';
    case 'projectName':
      return task.projectName || '未知项目';
    case 'constructionCompany' as TaskColumnId:
      return task.constructionCompany || '未知施工单位';
    case 'constructionLocation' as TaskColumnId:
      return task.constructionLocation || '未知施工位置';
    case 'strength' as TaskColumnId:
      return task.strength || '未知强度';
    case 'pouringMethod' as TaskColumnId:
      return task.pouringMethod || '未知浇筑方式';
    case 'deliveryStatus' as TaskColumnId:
      return task.deliveryStatus || '未知配送状态';
    case 'plantId' as TaskColumnId:
      return task.plantId || '未知工厂';
    case 'none':
    default:
      return 'all';
  }
}

/**
 * 获取分组的显示标签
 */
export function getGroupLabel(groupKey: string, groupBy: TaskGroupConfig['groupBy']): string {
  if (groupBy === 'none') return '所有任务';
  
  const labelMap: Record<string, string> = {
    '未分派': '🔄 未分派',
    '已分派': '✅ 已分派',
    '配送中': '🚛 配送中',
    '已完成': '✨ 已完成',
    '未知项目': '❓ 未知项目',
    '未知施工单位': '❓ 未知施工单位',
    '未知施工位置': '📍 未知位置',
    '未知强度': '❓ 未知强度',
    '未知浇筑方式': '❓ 未知方式',
    '未知配送状态': '❓ 未知状态',
    '未知工厂': '🏭 未知工厂',
  };
  
  return labelMap[groupKey] || groupKey;
}

/**
 * 计算分组统计信息
 */
export function calculateGroupStats(tasks: Task[]): TaskGroupStats {
  const total = tasks.length;
  const dispatched = tasks.filter(task => task.dispatchStatus === 'InProgress').length;
  const inDelivery = tasks.filter(task => task.deliveryStatus === 'InDelivery').length;
  const completed = tasks.filter(task => task.deliveryStatus === 'Completed').length;
  
  return {
    total,
    dispatched,
    inDelivery,
    completed,
    pending: total - dispatched - inDelivery - completed
  };
}


/**
 * 对任务进行分组
 */
export function groupTasks(tasks: Task[], config: TaskGroupConfig): TaskGroup[] {
  if (!config.enabled || config.groupBy === 'none') {
    return [{
      key: 'all',
      label: '所有任务',
      tasks,
      collapsed: false,
    }];
  }

  // 按分组字段分组
  const groupMap = new Map<string, Task[]>();
  
  tasks.forEach(task => {
    const groupKey = getTaskGroupKey(task, config.groupBy);
    if (!groupMap.has(groupKey)) {
      groupMap.set(groupKey, []);
    }
    groupMap.get(groupKey)!.push(task);
  });

  // 转换为TaskGroup数组
  const groups: TaskGroup[] = Array.from(groupMap.entries()).map(([key, groupTasks]) => ({
    key,
    label: getGroupLabel(key, config.groupBy),
    tasks: groupTasks,
    collapsed: config.defaultCollapsed?.includes(key) || false,
  }));

  // 排序
  groups.sort((a, b) => {
    const aValue = a.key;
    const bValue = b.key;
    
    if (config.sortOrder === 'desc') {
      return bValue.localeCompare(aValue, 'zh-CN');
    }
    return aValue.localeCompare(bValue, 'zh-CN');
  });

  return groups;
}

/**
 * 获取可用的分组选项
 */
export function getGroupingOptions(config?: TaskGroupConfig): Array<{
  value: TaskGroupConfig['groupBy'];
  label: string;
  icon: string;
}> {
  const allOptions = [
    { value: 'none', label: '不分组', icon: '📋' },
    { value: 'dispatchStatus', label: '按分派状态', icon: '🔄' },
    { value: 'projectName', label: '按项目名称', icon: '🏗️' },
    { value: 'constructionCompany', label: '按施工单位', icon: '🏢' },
    { value: 'constructionLocation', label: '按施工位置', icon: '📍' },
    { value: 'strength', label: '按混凝土强度', icon: '💪' },
    { value: 'pouringMethod', label: '按浇筑方式', icon: '🔧' },
    { value: 'deliveryStatus', label: '按配送状态', icon: '🚛' },
    { value: 'plantId', label: '按工厂', icon: '🏭' },
  ] as const;

  // 如果没有提供配置，返回所有选项
  if (!config) {
    return allOptions.map(option => ({
      value: option.value as ('none' | TaskColumnId),
      label: option.label,
      icon: option.icon
    }));
  }

  // 根据配置过滤选项
  return allOptions.filter((option): option is typeof allOptions[number] => {
    if (option.value === 'none') return true; // 总是允许"不分组"
    
    const columnId = option.value as TaskColumnId;
    const isDisallowed = config.disallowedGroupColumns.includes(columnId);
    const isAllowed = config.allowedGroupColumns.includes(columnId);
    
    // 如果在禁止列表中，不显示
    if (isDisallowed) return false;
    
    // 如果在允许列表中，显示
    if (isAllowed) return true;
    
    // 如果既不在允许列表也不在禁止列表中，默认不显示
    return false;
  });
}


