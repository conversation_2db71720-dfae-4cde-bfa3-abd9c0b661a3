// src/contexts/TaskSelectionContext.tsx
'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import type { Task } from '@/types';

interface TaskSelectionContextType {
  // 单选模式
  selectedTask: Task | null;
  selectedTaskId: string | null;
  setSelectedTask: (task: Task | null) => void;
  setSelectedTaskId: (taskId: string | null) => void;
  isTaskSelected: (taskId: string) => boolean;
  clearSelection: () => void;

  // 多选模式
  isMultiSelectMode: boolean;
  setMultiSelectMode: (enabled: boolean) => void;
  selectedTaskIds: string[];
  selectedTasks: Task[];
  toggleTaskSelection: (task: Task) => void;
  selectAllTasks: (tasks: Task[]) => void;
  clearAllSelections: () => void;
  isTaskMultiSelected: (taskId: string) => boolean;
}

const TaskSelectionContext = createContext<TaskSelectionContextType | undefined>(undefined);

interface TaskSelectionProviderProps {
  children: React.ReactNode;
}

export const TaskSelectionProvider: React.FC<TaskSelectionProviderProps> = ({ children }) => {
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);

  const handleSetSelectedTask = useCallback((task: Task | null) => {
    console.log('=== handleSetSelectedTask called ===');
    console.log('Received task:', task?.taskNumber, task?.id);
    console.log('Previous selectedTask:', selectedTask?.taskNumber);
    console.log('Previous selectedTaskId:', selectedTaskId);

    setSelectedTask(task);
    setSelectedTaskId(task?.id || null);

    console.log('Updated to task:', task?.taskNumber, task?.id);
    console.log('=== handleSetSelectedTask end ===');
  }, [selectedTask, selectedTaskId]);

  const handleSetSelectedTaskId = useCallback((taskId: string | null) => {
    setSelectedTaskId(taskId);
    // 如果只设置ID，清除task对象，让组件自己查找
    if (taskId !== selectedTask?.id) {
      setSelectedTask(null);
    }
  }, [selectedTask?.id]);

  const isTaskSelected = useCallback((taskId: string) => {
    return selectedTaskId === taskId;
  }, [selectedTaskId]);

  const clearSelection = useCallback(() => {
    setSelectedTask(null);
    setSelectedTaskId(null);
  }, []);

  const contextValue: TaskSelectionContextType = {
    selectedTask,
    selectedTaskId,
    setSelectedTask: handleSetSelectedTask,
    setSelectedTaskId: handleSetSelectedTaskId,
    isTaskSelected,
    clearSelection,
    isMultiSelectMode: false,
    setMultiSelectMode: function (enabled: boolean): void {
      throw new Error('Function not implemented.');
    },
    selectedTaskIds: [],
    selectedTasks: [],
    toggleTaskSelection: function (task: Task): void {
      if (this.isMultiSelectMode) {
        // 多选模式下切换任务选中状态
        this.toggleTaskSelection(task);
      } else {
        // 单选模式下切换任务选中状态
        this.setSelectedTask(task);
      }
    },
    selectAllTasks: function (tasks: Task[]): void {
      this.setMultiSelectMode(true);
      this.selectedTasks = tasks;
      this.selectedTaskIds = tasks.map(task => task.id);
    },
    clearAllSelections: function (): void {
      this.setMultiSelectMode(false);
      this.selectedTasks = [];
      this.selectedTaskIds = [];
    },
    isTaskMultiSelected: function (taskId: string): boolean {
      return this.selectedTaskIds.includes(taskId);
    }
  };

  return (
    <TaskSelectionContext.Provider value={contextValue}>
      {children}
    </TaskSelectionContext.Provider>
  );
};

export const useTaskSelection = (): TaskSelectionContextType => {
  const context = useContext(TaskSelectionContext);
  if (!context) {
    throw new Error('useTaskSelection must be used within a TaskSelectionProvider');
  }
  return context;
};

// 简化的hook，只用于检查选中状态
export const useTaskSelectionState = () => {
  const context = useTaskSelection();
  
  return {
    selectedTaskId: context.selectedTaskId,
    selectedTask: context.selectedTask,
    isTaskSelected: context.isTaskSelected,
  };
};

// 简化的hook，用于设置选中状态
export const useTaskSelectionActions = () => {
  const context = useTaskSelection();
  
  return {
    setSelectedTask: context.setSelectedTask,
    setSelectedTaskId: context.setSelectedTaskId,
    clearSelection: context.clearSelection,
  };
};
