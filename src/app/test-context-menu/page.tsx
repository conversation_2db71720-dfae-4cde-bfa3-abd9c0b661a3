'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { TaskListContextMenus } from '@/components/sections/task-list/task-list-context-menus';
import { ColumnVisibilityModal } from '@/components/modals/column-visibility-modal';
import { TaskCardConfigModal } from '@/components/sections/task-list/cards/TaskCardConfigModal';
import { MixRatioPageV2 } from '@/components/pages/mix-ratio-page-v2';
import { useToast } from '@/hooks/use-toast';
import type { Task, TaskCardConfig } from '@/types';
import { defaultTaskCardConfig } from '@/types/taskCardConfig';
import { LayoutGrid, Table } from 'lucide-react';
import {ModernMixRatioPage} from "@/components/pages/modern-mix-ratio-page";

// 模拟任务数据
const mockTask: Task = {
  id: 'test-task-1',
  plantId: 'plant-1',
  taskNumber: 'T001',
  projectName: '测试工程项目',
  projectAbbreviation: '测试项目',
  constructionUnit: '测试建设单位',
  constructionSite: '地下室基础',
  strength: 'C30',
  pouringMethod: '泵送',
  vehicleCount: 5,
  completedVolume: 50,
  requiredVolume: 100,
  pumpTruck: '62米',
  otherRequirements: '无特殊要求',
  contactPhone: '13800138000',
  supplyTime: '08:00',
  supplyDate: '2024-01-15',
  publishDate: '2024-01-14',
  dispatchStatus: 'ReadyToProduce',
  vehicles: [],
  deliveryStatus: 'pending',
  dispatchReminderMinutes: 30,
  timing: '08:00:00',
  isTicketed: false,
  constructionCompany: '中建三局集团有限公司',
  constructionLocation: '地下室基础浇筑',
  productionLineCount: 2,
  customerName: '测试客户',
  address: '测试地址123号',
  notes: '这是一个测试任务',
  createdAt: '2024-01-14T10:00:00Z',
  scheduledTime: '2024-01-15T08:00:00Z',
  estimatedDuration: 120,
  unreadMessageCount: 0,
  hasNewMessages: false,
  messages: []
};

const mockTaskCardConfig: TaskCardConfig = defaultTaskCardConfig;

export default function TestContextMenuPage() {
  const [displayMode, setDisplayMode] = useState<'table' | 'card'>('table');
  const [isContextMenuOpen, setIsContextMenuOpen] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number } | null>(null);
  const [isColumnModalOpen, setIsColumnModalOpen] = useState(false);
  const [isCardConfigModalOpen, setIsCardConfigModalOpen] = useState(false);
  const [taskCardConfig, setTaskCardConfig] = useState<TaskCardConfig>(mockTaskCardConfig);
  const [isMixRatioPageOpen, setIsMixRatioPageOpen] = useState(false);
  const { toast } = useToast();

  const handleRightClick = (event: React.MouseEvent) => {
    event.preventDefault();
    setContextMenuPosition({ x: event.clientX, y: event.clientY });
    setIsContextMenuOpen(true);
  };

  const handleCloseContextMenu = () => {
    setIsContextMenuOpen(false);
    setContextMenuPosition(null);
  };

  const handleSetDisplayMode = (mode: 'table' | 'card') => {
    setDisplayMode(mode);
    toast({
      title: '显示模式已切换',
      description: `已切换到${mode === 'table' ? '表格' : '卡片'}模式`,
    });
    handleCloseContextMenu();
  };

  const handleOpenColumnSettings = () => {
    setIsColumnModalOpen(true);
    handleCloseContextMenu();
  };

  const handleOpenCardConfig = () => {
    setIsCardConfigModalOpen(true);
    handleCloseContextMenu();
  };

  const handleTaskCardConfigChange = (newConfig: Partial<TaskCardConfig>) => {
    setTaskCardConfig(prev => ({ ...prev, ...newConfig }));
  };

  const handleOpenMixRatioPage = (task: Task) => {
    console.log('打开配比页面:', task);
    setIsMixRatioPageOpen(true);
    setIsContextMenuOpen(false);
  };

  const handleCloseMixRatioPage = () => {
    setIsMixRatioPageOpen(false);
  };

  return (
    <div className="container mx-auto p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">任务列表右键菜单功能测试</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">当前状态</h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="p-3 bg-blue-50 rounded flex items-center gap-2">
              {displayMode === 'table' ? (
                <Table className="h-4 w-4 text-blue-600" />
              ) : (
                <LayoutGrid className="h-4 w-4 text-blue-600" />
              )}
              <span className="font-medium text-blue-800">当前显示模式:</span> 
              <span className="ml-2">{displayMode === 'table' ? '表格模式' : '卡片模式'}</span>
            </div>
            <div className="p-3 bg-green-50 rounded">
              <span className="font-medium text-green-800">任务数量:</span> 
              <span className="ml-2">1 个测试任务</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-8 mb-8 min-h-[200px] flex items-center justify-center">
          <div 
            className="bg-white rounded-lg shadow p-6 cursor-pointer hover:shadow-md transition-shadow"
            onContextMenu={handleRightClick}
          >
            <h3 className="font-semibold mb-2">测试任务区域</h3>
            <p className="text-sm text-gray-600 mb-4">
              在此区域右键点击以测试右键菜单功能
            </p>
            <div className="text-xs text-gray-500">
              <div>任务编号: {mockTask.taskNumber}</div>
              <div>工程名称: {mockTask.projectName}</div>
              <div>施工部位: {mockTask.constructionSite}</div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">表格模式功能</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 右键 → "设置显示列" → 打开列显示与排序弹框</li>
              <li>• 右键 → "设置显示模式" → 选择表格/卡片模式</li>
              <li>• 右键 → "修改配比" → 打开配比管理页面</li>
              <li>• 可以拖拽调整列顺序</li>
              <li>• 可以显示/隐藏列</li>
            </ul>
          </div>
          
          <div className="p-4 bg-green-50 rounded-lg">
            <h3 className="font-semibold text-green-800 mb-2">卡片模式功能</h3>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• 右键 → "设置显示列" → 打开卡片配置弹框</li>
              <li>• 右键 → "设置显示模式" → 选择表格/卡片模式</li>
              <li>• 右键 → "修改配比" → 打开配比管理页面</li>
              <li>• 可以配置卡片显示字段</li>
              <li>• 可以调整卡片样式和主题</li>
            </ul>
          </div>
        </div>

        <div className="p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-semibold text-yellow-800 mb-2">测试步骤</h3>
          <ol className="text-sm text-yellow-700 space-y-1 list-decimal list-inside">
            <li>在上方的"测试任务区域"右键点击</li>
            <li>观察右键菜单中的各种选项</li>
            <li>点击"设置显示列"，根据当前模式打开对应的弹框</li>
            <li>点击"修改配比"，打开配比管理页面</li>
            <li>测试"设置显示模式"的二级菜单功能</li>
            <li>切换显示模式后再次测试右键菜单</li>
          </ol>
        </div>

        {/* 快速切换按钮 */}
        <div className="flex justify-center gap-4 mt-6">
          <Button 
            variant={displayMode === 'table' ? 'default' : 'outline'}
            onClick={() => handleSetDisplayMode('table')}
          >
            <Table className="mr-2 h-4 w-4" />
            表格模式
          </Button>
          <Button 
            variant={displayMode === 'card' ? 'default' : 'outline'}
            onClick={() => handleSetDisplayMode('card')}
          >
            <LayoutGrid className="mr-2 h-4 w-4" />
            卡片模式
          </Button>
        </div>
      </div>

      {/* 右键菜单 */}
      <TaskListContextMenus
        isTaskContextMenuOpen={isContextMenuOpen}
        taskContextMenuPosition={contextMenuPosition}
        contextMenuTaskData={{ taskId: mockTask.id }}
        closeTaskContextMenu={handleCloseContextMenu}
        filteredTasks={[mockTask]}
        openTankerNoteModal={() => console.log('打开罐车备注')}
        openReminderConfigModal={() => console.log('打开提醒配置')}
        onOpenTimeSettingsModal={() => console.log('打开时间设置')}
        onPauseTask={() => console.log('暂停任务')}
        onCompleteTask={() => console.log('完成任务')}
        onOpenDispatchDetailsModal={() => console.log('打开调度详情')}
        onOpenTimeStatsModal={() => console.log('打开时间统计')}
        onOpenTaskDetailModal={() => console.log('打开任务详情')}
        onOpenTaskProgressModal={() => console.log('打开任务进度')}
        onOpenRatioDisplayModal={() => console.log('显示配比')}
        onOpenRatioEditModal={() => console.log('编辑配比')}
        onOpenColumnSettingsModal={handleOpenColumnSettings}
        onOpenCardConfigModal={handleOpenCardConfig}
        onSetDisplayMode={handleSetDisplayMode}
        onResetToDefaultStyle={() => console.log('重置默认样式')}
        onOpenTaskAbbreviationModal={() => console.log('打开任务缩写')}
        onPublishWeChatMessage={() => console.log('发布微信消息')}
        onPrintQRCode={() => console.log('打印二维码')}
        onOpenMixRatioPage={handleOpenMixRatioPage}
        currentDisplayMode={displayMode}
        isVehicleCardContextMenuOpen={false}
        vehicleCardContextMenuPosition={null}
        vehicleCardContextMenuContext={null}
        closeVehicleCardContextMenu={() => {}}
        openDeliveryOrderDetailsModal={() => {}}
        cancelVehicleDispatch={() => {}}
      />

      {/* 列显示与排序模态框 */}
      <ColumnVisibilityModal
        isOpen={isColumnModalOpen}
        onOpenChange={setIsColumnModalOpen}
        allColumns={[]}
        columnVisibility={{}}
        onColumnVisibilityChange={() => {}}
        currentOrder={[]}
        onOrderChange={() => {}}
      />

      {/* 卡片配置模态框 */}
      <TaskCardConfigModal
        open={isCardConfigModalOpen}
        onOpenChange={setIsCardConfigModalOpen}
        config={taskCardConfig}
        onConfigChange={handleTaskCardConfigChange}
        onSave={() => {
          toast({
            title: '配置已保存',
            description: '卡片配置已成功保存',
          });
          setIsCardConfigModalOpen(false);
        }}
        onCancel={() => setIsCardConfigModalOpen(false)}
      />

      {/* 配比页面 */}
      {isMixRatioPageOpen && (
        <ModernMixRatioPage
          task={mockTask}
          onClose={handleCloseMixRatioPage}
        />
      )}
    </div>
  );
}
