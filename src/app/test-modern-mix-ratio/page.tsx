'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calculator, ArrowLeft, AlertCircle } from 'lucide-react';
import { ModernMixRatioPage } from '@/components/pages/modern-mix-ratio-page';
import { ErrorBoundary } from '@/components/pages/mix-ratio/error-boundary';
import type { Task } from '@/types';

export default function TestModernMixRatioPage() {
  const [isPageOpen, setIsPageOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 模拟任务数据
  const mockTask: Task = {
    id: 'test-task-001',
    taskNumber: 'T001',
    projectName: '测试工程项目',
    customerName: '测试客户',
    constructionSite: '地下室基础',
    constructionUnit: '测试施工单位',
    strength: 'C30',
    pouringMethod: '泵送',
    requiredVolume: 120,
    completedVolume: 0,
    scheduledTime: '2024-01-15 08:00',
    contactPhone: '13800138000',
    status: 'pending',
    priority: 'normal',
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    vehicles: [],
    dispatchedVehicles: [],
    completedProgress: 0,
    estimatedDuration: 240,
    constructionLocation: '北京市朝阳区',
    taskStatus: '待生产',
    isUrgent: false,
    hasMessage: false,
    messageCount: 0,
    lastMessageTime: null,
    dispatchReminder: '',
    notes: '',
    tags: [],
    assignedOperator: '',
    plantId: 'plant-001',
    plantName: '搅拌站A',
    createdBy: 'system',
    updatedBy: 'system'
  };

  const openPage = () => {
    try {
      setError(null);
      setIsPageOpen(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    }
  };

  const closePage = () => {
    setIsPageOpen(false);
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => window.history.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回
          </Button>
          <h1 className="text-3xl font-bold">现代化配比页面测试</h1>
        </div>

        {/* 错误显示 */}
        {error && (
          <Card className="border-destructive">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2 text-destructive">
                <AlertCircle className="h-4 w-4" />
                <span className="font-medium">错误:</span>
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 功能说明 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              现代化配比页面特性
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-medium text-green-600">✅ 界面改进</h4>
                <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                  <li>现代化顶部操作栏</li>
                  <li>响应式计算参数表格</li>
                  <li>智能材料选择器</li>
                  <li>微调控件支持</li>
                  <li>状态徽章和图标</li>
                  <li>现代化底部操作栏</li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-medium text-blue-600">🔧 技术特点</h4>
                <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                  <li>TypeScript 类型安全</li>
                  <li>组件化设计</li>
                  <li>错误边界保护</li>
                  <li>响应式布局</li>
                  <li>现代化UI组件</li>
                  <li>状态管理优化</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 测试任务信息 */}
        <Card>
          <CardHeader>
            <CardTitle>测试任务信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">任务编号:</span>
                <div className="font-medium">{mockTask.taskNumber}</div>
              </div>
              <div>
                <span className="text-muted-foreground">工程名称:</span>
                <div className="font-medium">{mockTask.projectName}</div>
              </div>
              <div>
                <span className="text-muted-foreground">强度等级:</span>
                <div className="font-medium">{mockTask.strength}</div>
              </div>
              <div>
                <span className="text-muted-foreground">施工部位:</span>
                <div className="font-medium">{mockTask.constructionSite}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 测试按钮 */}
        <Card>
          <CardHeader>
            <CardTitle>功能测试</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                点击下方按钮打开现代化配比页面，体验全新的用户界面和交互方式。
              </div>
              
              <Button onClick={openPage} size="lg" className="w-full">
                <Calculator className="mr-2 h-5 w-5" />
                打开现代化配比页面
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 改进对比 */}
        <Card>
          <CardHeader>
            <CardTitle>主要改进对比</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">功能</th>
                    <th className="text-left p-2">原版</th>
                    <th className="text-left p-2">现代化版本</th>
                  </tr>
                </thead>
                <tbody className="text-muted-foreground">
                  <tr className="border-b">
                    <td className="p-2 font-medium">搅拌站选择</td>
                    <td className="p-2">简单下拉框</td>
                    <td className="p-2">
                      <Badge variant="outline" className="text-xs">现代化选择器 + 状态显示</Badge>
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-medium">计算参数</td>
                    <td className="p-2">传统表格</td>
                    <td className="p-2">
                      <Badge variant="outline" className="text-xs">响应式网格 + 微调控件</Badge>
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-medium">材料选择</td>
                    <td className="p-2">基础下拉框</td>
                    <td className="p-2">
                      <Badge variant="outline" className="text-xs">智能选择器 + 搜索筛选</Badge>
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-medium">状态显示</td>
                    <td className="p-2">文本显示</td>
                    <td className="p-2">
                      <Badge variant="outline" className="text-xs">彩色徽章 + 图标</Badge>
                    </td>
                  </tr>
                  <tr>
                    <td className="p-2 font-medium">布局设计</td>
                    <td className="p-2">固定布局</td>
                    <td className="p-2">
                      <Badge variant="outline" className="text-xs">响应式设计</Badge>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 现代化配比页面 */}
      {isPageOpen && (
        <ErrorBoundary>
          <ModernMixRatioPage
            task={mockTask}
            onClose={closePage}
          />
        </ErrorBoundary>
      )}
    </div>
  );
}
