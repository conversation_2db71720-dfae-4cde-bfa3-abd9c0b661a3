'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calculator, ArrowLeft } from 'lucide-react';
import { MixRatioPage } from '@/components/pages/mix-ratio-page';
import type { Task } from '@/types';

export default function TestMixRatioPage() {
  const [isMixRatioPageOpen, setIsMixRatioPageOpen] = useState(false);

  // 模拟任务数据
  const mockTask: Task = {
    id: 'test-task-001',
    taskNumber: 'T001',
    projectName: '测试工程项目',
    customerName: '测试客户',
    constructionSite: '地下室基础',
    constructionUnit: '测试施工单位',
    strength: 'C30',
    pouringMethod: '泵送',
    requiredVolume: 120,
    completedVolume: 0,
    scheduledTime: '2024-01-15 08:00',
    contactPhone: '13800138000',
    status: 'pending',
    priority: 'normal',
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    vehicles: [],
    dispatchedVehicles: [],
    completedProgress: 0,
    estimatedDuration: 240,
    constructionLocation: '北京市朝阳区',
    taskStatus: '待生产',
    isUrgent: false,
    hasMessage: false,
    messageCount: 0,
    lastMessageTime: null,
    dispatchReminder: '',
    notes: '',
    tags: [],
    assignedOperator: '',
    plantId: 'plant-001',
    plantName: '搅拌站A',
    createdBy: 'system',
    updatedBy: 'system'
  };

  const openMixRatioPage = () => {
    setIsMixRatioPageOpen(true);
  };

  const closeMixRatioPage = () => {
    setIsMixRatioPageOpen(false);
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => window.history.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回
          </Button>
          <h1 className="text-3xl font-bold">配比页面功能测试</h1>
        </div>

        {/* 功能说明 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              配比页面功能说明
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                配比页面是一个完整的混凝土配比管理系统，包含以下主要功能：
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">操作栏功能</h4>
                  <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                    <li>所有站统一配比设置</li>
                    <li>搅拌站选择和管理</li>
                    <li>材料名称管理</li>
                    <li>存放地名称管理（料仓管理）</li>
                    <li>配比历史记录查看</li>
                    <li>参数设定和质量标准</li>
                  </ul>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium">核心功能</h4>
                  <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                    <li>任务信息显示和编辑</li>
                    <li>配比计算和反算功能</li>
                    <li>材料配比表格管理</li>
                    <li>物料信息面板</li>
                    <li>配比保存和审核发送</li>
                    <li>同步选用配比功能</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 测试任务信息 */}
        <Card>
          <CardHeader>
            <CardTitle>测试任务信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">任务编号:</span>
                <div className="font-medium">{mockTask.taskNumber}</div>
              </div>
              <div>
                <span className="text-muted-foreground">工程名称:</span>
                <div className="font-medium">{mockTask.projectName}</div>
              </div>
              <div>
                <span className="text-muted-foreground">强度等级:</span>
                <div className="font-medium">{mockTask.strength}</div>
              </div>
              <div>
                <span className="text-muted-foreground">施工部位:</span>
                <div className="font-medium">{mockTask.constructionSite}</div>
              </div>
              <div>
                <span className="text-muted-foreground">需求方量:</span>
                <div className="font-medium">{mockTask.requiredVolume}m³</div>
              </div>
              <div>
                <span className="text-muted-foreground">浇筑方式:</span>
                <div className="font-medium">{mockTask.pouringMethod}</div>
              </div>
              <div>
                <span className="text-muted-foreground">联系电话:</span>
                <div className="font-medium">{mockTask.contactPhone}</div>
              </div>
              <div>
                <span className="text-muted-foreground">状态:</span>
                <Badge variant="secondary">{mockTask.status}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 测试按钮 */}
        <Card>
          <CardHeader>
            <CardTitle>功能测试</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                点击下方按钮打开配比页面，测试完整的配比管理功能。
              </div>
              
              <Button onClick={openMixRatioPage} size="lg" className="w-full">
                <Calculator className="mr-2 h-5 w-5" />
                打开配比页面
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 功能特点 */}
        <Card>
          <CardHeader>
            <CardTitle>功能特点</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-medium text-green-600">✅ 已实现功能</h4>
                <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                  <li>完整的配比页面布局</li>
                  <li>任务信息管理</li>
                  <li>配比计算和反算</li>
                  <li>材料管理和料仓管理</li>
                  <li>配比历史记录</li>
                  <li>质量标准设置</li>
                  <li>参数配置管理</li>
                  <li>物料信息显示</li>
                  <li>配比表格操作</li>
                  <li>保存和审核功能</li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-medium text-blue-600">🔧 技术特点</h4>
                <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                  <li>模块化组件设计</li>
                  <li>TypeScript 类型安全</li>
                  <li>响应式布局设计</li>
                  <li>完整的状态管理</li>
                  <li>用户友好的交互</li>
                  <li>数据验证和错误处理</li>
                  <li>可扩展的架构</li>
                  <li>美观的UI设计</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 配比页面 */}
      {isMixRatioPageOpen && (
        <MixRatioPage
          task={mockTask}
          onClose={closeMixRatioPage}
        />
      )}
    </div>
  );
}
